'use client';

export default function PackageMedia({ package: pkg, onPackageUpdate, onHasChanges }) {
  return (
    <div className="space-y-6">
      <div className="text-center py-12">
        <div className="text-6xl mb-4">🖼️</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Media Management</h3>
        <p className="text-gray-600 mb-4">
          Upload and manage package images, videos, and other media assets.
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm text-blue-800">
          <strong>Coming Soon:</strong> Drag-and-drop image upload, image optimization, 
          gallery management, and primary image selection.
        </div>
      </div>
    </div>
  );
}
