'use client';

import React, { useRef, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import LoadingComponent from '@/components/LoadingComponent';

// Helper function to determine video type from URL
function getVideoType(url) {
  if (!url) return 'video/mp4'; // Default type

  try {
    // For Firebase Storage URLs that don't have file extensions in the URL
    if (url.includes('firebasestorage.googleapis.com')) {
      // Check if there's a token parameter that might contain the file name
      const fileNameMatch = url.match(/([^/?&]+\.(mp4|webm|ogg|mov))/i);
      if (fileNameMatch) {
        const extension = fileNameMatch[2].toLowerCase();
        return `video/${extension === 'mov' ? 'mp4' : extension}`;
      }

      // If we can't determine from URL, default to mp4
      return 'video/mp4';
    }

    // For regular URLs with file extensions
    const extension = url.split('.').pop().toLowerCase();
    switch (extension) {
      case 'mp4':
        return 'video/mp4';
      case 'webm':
        return 'video/webm';
      case 'ogg':
        return 'video/ogg';
      case 'mov':
        return 'video/mp4'; // Most browsers handle .mov files as mp4
      default:
        return 'video/mp4'; // Default to mp4
    }
  } catch (error) {
    // Silently handle any errors and return default type
    return 'video/mp4';
  }
}

export default function VideoStream({
  src,
  autoPlay = true,
  loop = false, // Changed default to false so we can redirect when video ends
  muted = true,
  controls = false,
  className = '',
  poster = '',
  preload = 'auto', // Used directly in video element
  playsInline = true, // Used directly in video element
  onEnded = null, // New prop for custom end behavior
  onError = null, // New prop for error handling
  redirectTo = null // New prop for redirect path
}) {
  const videoRef = useRef(null);
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  // Safely handle video errors
  const handleError = (error) => {
    setIsLoading(false);

    // Call custom error handler if provided
    if (typeof onError === 'function') {
      onError(error);
    }
  };

  // Handle autoplay on mount with enhanced reliability
  useEffect(() => {
    if (!src) {
      setIsLoading(false);
      return;
    }

    const videoElement = videoRef.current;
    if (!videoElement) return;

    if (autoPlay) {
      // Set up event listeners for better autoplay handling
      const handleCanPlay = () => {
        try {
          // Try to play the video as soon as it can play
          const playPromise = videoElement.play();

          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                // Autoplay started successfully
                setIsLoading(false);
              })
              .catch(() => {
                // Autoplay was prevented
                // Try again with muted (browsers are more likely to allow muted autoplay)
                videoElement.muted = true;

                videoElement.play()
                  .then(() => {
                    setIsLoading(false);
                  })
                  .catch(() => {
                    setIsLoading(false);
                    // Don't log errors here - just silently fail
                  });
              });
          }
        } catch (error) {
          // Silently handle any play errors
          setIsLoading(false);
        }
      };

      // Add event listeners for loading/buffering states
      const handleWaiting = () => {
        setIsLoading(true); // Video is buffering/waiting for more data
      };

      const handlePlaying = () => {
        setIsLoading(false); // Video is playing again after buffering
      };

      // Define event handlers for loadstart and canplaythrough
      const handleLoadStart = () => setIsLoading(true);
      const handleCanPlayThrough = () => setIsLoading(false);

      // Handle video errors
      const handleVideoError = (e) => {
        handleError(e);
      };

      // Add event listeners
      videoElement.addEventListener('canplay', handleCanPlay);
      videoElement.addEventListener('waiting', handleWaiting);
      videoElement.addEventListener('playing', handlePlaying);
      videoElement.addEventListener('loadstart', handleLoadStart);
      videoElement.addEventListener('canplaythrough', handleCanPlayThrough);
      videoElement.addEventListener('error', handleVideoError);

      // Try to play immediately as well
      if (videoElement.readyState >= 3) { // HAVE_FUTURE_DATA or higher
        handleCanPlay();
      }

      return () => {
        // Cleanup: remove event listeners and pause video when component unmounts
        videoElement.removeEventListener('canplay', handleCanPlay);
        videoElement.removeEventListener('waiting', handleWaiting);
        videoElement.removeEventListener('playing', handlePlaying);
        videoElement.removeEventListener('loadstart', handleLoadStart);
        videoElement.removeEventListener('canplaythrough', handleCanPlayThrough);
        videoElement.removeEventListener('error', handleVideoError);

        try {
          videoElement.pause();
        } catch (error) {
          // Silently handle pause errors
        }
      };
    } else {
      setIsLoading(false);
    }

    return () => {
      // Cleanup: pause video when component unmounts
      if (videoElement) {
        try {
          videoElement.pause();
        } catch (error) {
          // Silently handle pause errors
        }
      }
    };
  }, [autoPlay, src, onError]);

  // Handle video end event
  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const handleEnded = () => {
      // If custom onEnded handler is provided, call it
      if (typeof onEnded === 'function') {
        onEnded();
      }

      // If redirectTo path is provided, navigate to that path
      if (redirectTo) {
        router.push(redirectTo);
      }
    };

    // Add event listener for the 'ended' event
    videoElement.addEventListener('ended', handleEnded);

    // Clean up the event listener when component unmounts
    return () => {
      videoElement.removeEventListener('ended', handleEnded);
    };
  }, [onEnded, redirectTo, router]);

  // Force muted for better autoplay chances
  // Most browsers require muted videos for autoplay
  const forceMuted = autoPlay ? true : muted;

  // Don't render anything if no source is provided
  if (!src) {
    return null;
  }

  return (
    <div className="w-full h-auto object-cover relative">
      <video
        ref={videoRef}
        className={`w-full object-cover ${className}`}
        autoPlay={autoPlay}
        loop={loop}
        muted={forceMuted}
        controls={controls}
        poster={poster}
        preload={preload}
        playsInline={playsInline}
        src={src}
        onError={handleError}
      >
        {/* Fallback for browsers that need source tag */}
        <source src={src} type={getVideoType(src)} />
        Your browser does not support the video tag.
      </video>

      {/* Show loading component when video is loading/buffering */}
      {isLoading && <LoadingComponent />}
    </div>
  );
}
