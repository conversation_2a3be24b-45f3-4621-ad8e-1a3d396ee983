import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { _360Settings } from '@/models/_360Model';
import { requireManagerAPI } from '@/lib/auth-utils';

// GET /api/360s/[id] - Get single 360 (manager/admin only)
export const GET = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    
    const item = await _360Settings.findById(id);
    
    if (!item) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: '360 not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: item,
    });
  } catch (error) {
    console.error('Error fetching 360:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch 360',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// PUT /api/360s/[id] - Update 360 (manager/admin only)
export const PUT = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    const body = await request.json();
    
    // Remove fields that shouldn't be updated directly
    delete body._id;
    delete body.createdAt;
    delete body.updatedAt;
    
    const updated360 = await _360Settings.findByIdAndUpdate(
      id,
      body,
      { 
        new: true, 
        runValidators: true 
      }
    );
    
    if (!updated360) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: '360 not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: updated360,
      message: '360 updated successfully',
    });
  } catch (error) {
    console.error('Error updating 360:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update 360',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// DELETE /api/360s/[id] - Delete 360 (manager/admin only)
export const DELETE = requireManagerAPI(async (request, { params }) => {
  try {
    await connectDB();
    
    const { id } = params;
    
    const deleted360 = await _360Settings.findByIdAndDelete(id);
    
    if (!deleted360) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: '360 not found',
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: deleted360,
      message: '360 deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting 360:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete 360',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
