'use client';

import { Inter } from 'next/font/google';
import './globals.css';
import Link from 'next/link';

const inter = Inter({ subsets: ['latin'] });

export default function RootLayout({ children, session }) {
  return (
    <html lang="en">
      <body className={`${inter.className} antialiased`}>
        <Link 
          href={`/admin/dashboard`} 
          className="absolute z-10 bottom-0 right-0 p-4 text-teal-500 underline-offset-1 capitalize"
        >dashboard</Link>
        {children}
      </body>
    </html>
  );
}
