'use client';

import { Inter } from 'next/font/google';
import './globals.css';
import Link from 'next/link';

const inter = Inter({ subsets: ['latin'] });

export default function RootLayout({ children, session }) {
  const link=['admin/dashboard','beta','booking'];
  return (
    <html lang="en">
      <body className={`${inter.className} bg-black antialiased`}>
        <div className="flex absolute w-fit h-fit flex-col-reverse items-end z-10 bottom-0 right-0 p-4 text-teal-500 underline-offset-1 capitalize gap-2">
          {link.map((i,index) =>
            <Link key={index} href={`/${i}`}>{i}</Link>
          )}
        </div>
        {children}
      </body>
    </html>
  );
}
