import mongoose from 'mongoose';
const { Schema } = mongoose;

const _360Schema = new Schema({
    name:{type:String,required:true}, // Now stores filename without extension
    originalFileName:{type:String,default:''}, // Stores the original filename with extension
    url:{type:String,required:true},  // Changed from required:true to default:'' to allow initial creation
    priority:{type:Number,default:0},
    fullPath:{type:String,default:''},
    markerList:{type:Array},
    cameraPosition:{type:Number,default:-0.0001},
    _360Rotation:{type:Number,default:-0.0001},
},{timestamps:true});

export const _360Settings = mongoose.models._360Settings||mongoose.model('_360Settings', _360Schema)