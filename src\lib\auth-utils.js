import { auth } from '@/auth';
import { redirect } from 'next/navigation';
import connectDB from '@/lib/mongodb';
import { User } from '@/models/User';

/**
 * Get the current authenticated user
 */
export async function getCurrentUser() {
  try {
    const session = await auth();
    return session?.user || null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Get the current user with full database information
 */
export async function getCurrentUserFromDB() {
  try {
    const session = await auth();
    if (!session?.user?.email) {
      return null;
    }

    await connectDB();
    const user = await User.findOne({ email: session.user.email });
    return user;
  } catch (error) {
    console.error('Error getting user from database:', error);
    return null;
  }
}

/**
 * Check if the current user has a specific role
 */
export async function hasRole(requiredRole) {
  try {
    const user = await getCurrentUser();
    if (!user) return false;

    const roleLevels = {
      guest: 0,
      user: 1,
      manager: 2,
      admin: 3,
    };

    const userLevel = roleLevels[user.role] || 0;
    const requiredLevel = roleLevels[requiredRole] || 0;

    return userLevel >= requiredLevel;
  } catch (error) {
    console.error('Error checking user role:', error);
    return false;
  }
}

/**
 * Check if the current user is an admin
 */
export async function isAdmin() {
  return await hasRole('admin');
}

/**
 * Check if the current user is a manager or admin
 */
export async function isManager() {
  return await hasRole('manager');
}

/**
 * Check if the current user is authenticated
 */
export async function isAuthenticated() {
  const user = await getCurrentUser();
  return !!user;
}

/**
 * Require authentication - redirect to sign in if not authenticated
 */
export async function requireAuth() {
  const user = await getCurrentUser();
  if (!user) {
    redirect('/auth/signin');
  }
  return user;
}

/**
 * Require a specific role - redirect to unauthorized if insufficient permissions
 */
export async function requireRole(requiredRole) {
  const user = await requireAuth();
  const hasPermission = await hasRole(requiredRole);
  
  if (!hasPermission) {
    redirect('/auth/unauthorized');
  }
  
  return user;
}

/**
 * Require admin role
 */
export async function requireAdmin() {
  return await requireRole('admin');
}

/**
 * Require manager role or higher
 */
export async function requireManager() {
  return await requireRole('manager');
}

/**
 * API middleware to require authentication
 */
export function requireAuthAPI(handler) {
  return async (request, context) => {
    try {
      const session = await auth();
      if (!session?.user) {
        return new Response(
          JSON.stringify({
            error: 'Unauthorized',
            message: 'Authentication required',
          }),
          {
            status: 401,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      }

      // Add user to request context
      request.user = session.user;
      return handler(request, context);
    } catch (error) {
      console.error('Auth middleware error:', error);
      return new Response(
        JSON.stringify({
          error: 'Internal Server Error',
          message: 'Authentication check failed',
        }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }
  };
}

/**
 * API middleware to require specific role
 */
export function requireRoleAPI(requiredRole) {
  return function(handler) {
    return async (request, context) => {
      try {
        const session = await auth();
        if (!session?.user) {
          return new Response(
            JSON.stringify({
              error: 'Unauthorized',
              message: 'Authentication required',
            }),
            {
              status: 401,
              headers: { 'Content-Type': 'application/json' },
            }
          );
        }

        const roleLevels = {
          guest: 0,
          user: 1,
          manager: 2,
          admin: 3,
        };

        const userLevel = roleLevels[session.user.role] || 0;
        const requiredLevel = roleLevels[requiredRole] || 0;

        if (userLevel < requiredLevel) {
          return new Response(
            JSON.stringify({
              error: 'Forbidden',
              message: 'Insufficient permissions',
              required: requiredRole,
              current: session.user.role,
            }),
            {
              status: 403,
              headers: { 'Content-Type': 'application/json' },
            }
          );
        }

        // Add user to request context
        request.user = session.user;
        return handler(request, context);
      } catch (error) {
        console.error('Role middleware error:', error);
        return new Response(
          JSON.stringify({
            error: 'Internal Server Error',
            message: 'Authorization check failed',
          }),
          {
            status: 500,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      }
    };
  };
}

/**
 * API middleware to require admin role
 */
export function requireAdminAPI(handler) {
  return requireRoleAPI('admin')(handler);
}

/**
 * API middleware to require manager role or higher
 */
export function requireManagerAPI(handler) {
  return requireRoleAPI('manager')(handler);
}

/**
 * Check if user owns a resource or has admin/manager privileges
 */
export async function canAccessResource(resourceUserId) {
  const user = await getCurrentUser();
  if (!user) return false;

  // Admin and managers can access all resources
  if (await hasRole('manager')) {
    return true;
  }

  // Users can only access their own resources
  return user.id === resourceUserId || user.id === resourceUserId.toString();
}

/**
 * Create or update guest user during booking/purchase
 */
export async function createOrUpdateGuestUser(guestData) {
  try {
    await connectDB();
    
    // Check if user already exists
    let user = await User.findOne({ email: guestData.email });
    
    if (user) {
      // Update existing user with new information
      const updateData = {
        phone: guestData.phone || user.phone,
        address: guestData.address || user.address,
        emergencyContact: guestData.emergencyContact || user.emergencyContact,
        $inc: { loginCount: 1 },
        lastLogin: new Date(),
      };

      // Handle name fields
      if (guestData.firstname && guestData.surname) {
        updateData.firstname = guestData.firstname;
        updateData.surname = guestData.surname;
        updateData.name = `${guestData.firstname} ${guestData.surname}`;
      } else if (guestData.name) {
        updateData.name = guestData.name;
      }

      user = await User.findByIdAndUpdate(user._id, updateData, { new: true });
    } else {
      // Create new guest user
      user = await User.createGuestUser(guestData);
    }
    
    return user;
  } catch (error) {
    console.error('Error creating/updating guest user:', error);
    throw error;
  }
}

/**
 * Promote guest user to regular user with password
 */
export async function promoteGuestUser(userId, password) {
  try {
    await connectDB();
    const user = await User.promoteGuestToUser(userId, password);
    return user;
  } catch (error) {
    console.error('Error promoting guest user:', error);
    throw error;
  }
}

/**
 * Get user role level as number
 */
export function getRoleLevel(role) {
  const roleLevels = {
    guest: 0,
    user: 1,
    manager: 2,
    admin: 3,
  };
  return roleLevels[role] || 0;
}

/**
 * Check if role A has higher or equal permissions than role B
 */
export function roleHasPermission(roleA, roleB) {
  return getRoleLevel(roleA) >= getRoleLevel(roleB);
}
