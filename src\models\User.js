import mongoose from 'mongoose';
import bcrypt from 'bcryptjs';

const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Name is required'],
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters'],
  },
  email: {
    type: String,
    required: [true, 'Email is required'],
    unique: true,
    lowercase: true,
    trim: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'],
  },
  password: {
    type: String,
    minlength: [6, 'Password must be at least 6 characters'],
    select: false, // Don't include password in queries by default
  },
  image: {
    type: String,
    default: null,
  },
  role: {
    type: String,
    enum: ['guest', 'user', 'manager', 'admin'],
    default: 'user',
  },
  provider: {
    type: String,
    enum: ['credentials', 'google', 'facebook', 'guest'],
    default: 'credentials',
  },
  isGuest: {
    type: Boolean,
    default: false,
  },
  emailVerified: {
    type: Date,
    default: null,
  },
  // Contact Information
  phone: {
    type: String,
    trim: true,
  },
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String,
  },
  // Emergency Contact
  emergencyContact: {
    name: String,
    phone: String,
    relationship: String,
  },
  // Preferences
  preferences: {
    newsletter: {
      type: Boolean,
      default: true,
    },
    notifications: {
      email: {
        type: Boolean,
        default: true,
      },
      sms: {
        type: Boolean,
        default: false,
      },
    },
    dietary: [String], // dietary restrictions
    accessibility: [String], // accessibility needs
  },
  // Authentication Tracking
  loginCount: {
    type: Number,
    default: 0,
  },
  lastLogin: {
    type: Date,
    default: null,
  },
  // Account Status
  isActive: {
    type: Boolean,
    default: true,
  },
  isBlocked: {
    type: Boolean,
    default: false,
  },
  // Notes (for staff use)
  notes: [{
    content: String,
    author: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
  }],
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for performance
UserSchema.index({ email: 1 });
UserSchema.index({ role: 1 });
UserSchema.index({ isActive: 1 });
UserSchema.index({ createdAt: -1 });
UserSchema.index({ lastLogin: -1 });

// Virtual for full name
UserSchema.virtual('fullName').get(function() {
  return this.name;
});

// Virtual for role level (for permission checking)
UserSchema.virtual('roleLevel').get(function() {
  const roleLevels = {
    guest: 0,
    user: 1,
    manager: 2,
    admin: 3,
  };
  return roleLevels[this.role] || 0;
});

// Pre-save middleware to hash password
UserSchema.pre('save', async function(next) {
  if (!this.isModified('password')) return next();
  
  if (this.password) {
    this.password = await bcrypt.hash(this.password, 12);
  }
  next();
});

// Instance method to check password
UserSchema.methods.comparePassword = async function(candidatePassword) {
  if (!this.password) return false;
  return await bcrypt.compare(candidatePassword, this.password);
};

// Instance method to check role permission
UserSchema.methods.hasRole = function(requiredRole) {
  const roleLevels = {
    guest: 0,
    user: 1,
    manager: 2,
    admin: 3,
  };
  
  const userLevel = roleLevels[this.role] || 0;
  const requiredLevel = roleLevels[requiredRole] || 0;
  
  return userLevel >= requiredLevel;
};

// Static method to create guest user
UserSchema.statics.createGuestUser = async function(guestData) {
  const guestUser = new this({
    name: guestData.name,
    email: guestData.email,
    phone: guestData.phone,
    role: 'guest',
    provider: 'guest',
    isGuest: true,
    address: guestData.address,
    emergencyContact: guestData.emergencyContact,
  });
  
  return await guestUser.save();
};

// Static method to promote guest to user
UserSchema.statics.promoteGuestToUser = async function(userId, password) {
  const hashedPassword = await bcrypt.hash(password, 12);
  
  return await this.findByIdAndUpdate(
    userId,
    {
      role: 'user',
      provider: 'credentials',
      isGuest: false,
      password: hashedPassword,
    },
    { new: true }
  );
};

export const User = mongoose.models.User || mongoose.model('User', UserSchema);
