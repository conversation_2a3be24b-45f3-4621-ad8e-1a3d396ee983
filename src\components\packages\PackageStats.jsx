'use client';

import { useMemo } from 'react';

export default function PackageStats({ packages }) {
  const stats = useMemo(() => {
    if (!packages || packages.length === 0) {
      return {
        total: 0,
        active: 0,
        featured: 0,
        totalRevenue: 0,
        totalBookings: 0,
        averagePrice: 0,
        topPerformers: [],
        byCategory: {},
        priceRanges: {},
      };
    }

    const calculations = {
      total: packages.length,
      active: packages.filter(p => p.availability?.isActive).length,
      featured: packages.filter(p => p.featured).length,
      totalRevenue: 0,
      totalBookings: 0,
      byCategory: {},
      priceRanges: {
        budget: 0,    // < $500
        mid: 0,       // $500-$1000
        luxury: 0,    // > $1000
      },
    };

    let totalPriceSum = 0;
    let priceCount = 0;

    packages.forEach(pkg => {
      // Revenue and booking stats
      if (pkg.stats?.totalRevenue) {
        calculations.totalRevenue += pkg.stats.totalRevenue;
      }
      if (pkg.stats?.totalBookings) {
        calculations.totalBookings += pkg.stats.totalBookings;
      }

      // Category breakdown
      calculations.byCategory[pkg.category] = (calculations.byCategory[pkg.category] || 0) + 1;

      // Price analysis (using couples price as base)
      if (pkg.pricing?.couples?.price) {
        const price = pkg.pricing.couples.price;
        totalPriceSum += price;
        priceCount++;

        if (price < 500) {
          calculations.priceRanges.budget++;
        } else if (price <= 1000) {
          calculations.priceRanges.mid++;
        } else {
          calculations.priceRanges.luxury++;
        }
      }
    });

    // Calculate averages
    calculations.averagePrice = priceCount > 0 ? totalPriceSum / priceCount : 0;

    // Get top performers by revenue
    calculations.topPerformers = packages
      .filter(p => p.stats?.totalRevenue > 0)
      .sort((a, b) => (b.stats.totalRevenue || 0) - (a.stats.totalRevenue || 0))
      .slice(0, 5)
      .map(p => ({
        id: p._id,
        name: p.name,
        category: p.category,
        totalRevenue: p.stats.totalRevenue || 0,
        totalBookings: p.stats.totalBookings || 0,
        averageRating: p.stats.averageRating || 0,
      }));

    return calculations;
  }, [packages]);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatNumber = (num) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const getCategoryIcon = (category) => {
    const icons = {
      accommodation: '🏠',
      experience: '🎯',
      combo: '📦',
      seasonal: '🌟',
    };
    return icons[category] || '📋';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total Packages */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Total Packages</dt>
              <dd className="text-lg font-medium text-gray-900">{formatNumber(stats.total)}</dd>
            </dl>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center text-sm">
            <span className="text-green-600 font-medium">{stats.active}</span>
            <span className="text-gray-500 ml-1">active</span>
            <span className="text-gray-400 mx-2">•</span>
            <span className="text-blue-600 font-medium">{stats.featured}</span>
            <span className="text-gray-500 ml-1">featured</span>
          </div>
        </div>
      </div>

      {/* Total Revenue */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Total Revenue</dt>
              <dd className="text-lg font-medium text-gray-900">{formatCurrency(stats.totalRevenue)}</dd>
            </dl>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center text-sm">
            <span className="text-gray-600">
              {formatCurrency(stats.averagePrice)} avg price
            </span>
          </div>
        </div>
      </div>

      {/* Total Bookings */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Total Bookings</dt>
              <dd className="text-lg font-medium text-gray-900">{formatNumber(stats.totalBookings)}</dd>
            </dl>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center text-sm">
            <span className="text-gray-600">
              {stats.total > 0 ? (stats.totalBookings / stats.total).toFixed(1) : 0} avg per package
            </span>
          </div>
        </div>
      </div>

      {/* Performance Rate */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
              <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">Active Rate</dt>
              <dd className="text-lg font-medium text-gray-900">
                {stats.total > 0 ? ((stats.active / stats.total) * 100).toFixed(1) : 0}%
              </dd>
            </dl>
          </div>
        </div>
        <div className="mt-4">
          <div className="flex items-center text-sm">
            <span className="text-gray-600">
              {stats.total - stats.active} inactive
            </span>
          </div>
        </div>
      </div>

      {/* Category Distribution */}
      <div className="bg-white shadow rounded-lg p-6 md:col-span-2">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Package Distribution</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">By Category</h4>
            <div className="space-y-2">
              {Object.entries(stats.byCategory).map(([category, count]) => (
                <div key={category} className="flex justify-between text-sm">
                  <span className="flex items-center text-gray-600">
                    <span className="mr-2">{getCategoryIcon(category)}</span>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </span>
                  <span className="font-medium">{count}</span>
                </div>
              ))}
            </div>
          </div>
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">By Price Range</h4>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Budget (&lt; $500)</span>
                <span className="font-medium">{stats.priceRanges.budget}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Mid-range ($500-$1000)</span>
                <span className="font-medium">{stats.priceRanges.mid}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Luxury (&gt; $1000)</span>
                <span className="font-medium">{stats.priceRanges.luxury}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Top Performers */}
      <div className="bg-white shadow rounded-lg p-6 md:col-span-2">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Top Performing Packages</h3>
        {stats.topPerformers.length > 0 ? (
          <div className="space-y-3">
            {stats.topPerformers.map((pkg, index) => (
              <div key={pkg.id} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center text-white text-xs font-bold mr-3">
                    {index + 1}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-900">{pkg.name}</div>
                    <div className="text-xs text-gray-500 capitalize">{pkg.category}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    {formatCurrency(pkg.totalRevenue)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {pkg.totalBookings} bookings
                    {pkg.averageRating > 0 && (
                      <span className="ml-2">⭐ {pkg.averageRating.toFixed(1)}</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center text-gray-500 py-4">
            No performance data available yet
          </div>
        )}
      </div>
    </div>
  );
}
