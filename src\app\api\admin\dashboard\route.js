import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { User } from '@/models/User';
import { Package } from '@/models/Package';
import { Booking } from '@/models/Booking';
import { Payment } from '@/models/Payment';
import { requireManagerAPI } from '@/lib/auth-utils';

// GET /api/admin/dashboard - Get dashboard statistics (manager/admin only)
export const GET = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(period));
    
    // Get overview statistics
    const stats = await Promise.all([
      getTotalStats(),
      getRecentStats(startDate),
      getBookingStats(startDate),
      getRevenueStats(startDate),
      getUpcomingBookings(),
      getRecentActivity(),
    ]);
    
    const [
      totalStats,
      recentStats,
      bookingStats,
      revenueStats,
      upcomingBookings,
      recentActivity,
    ] = stats;
    
    return NextResponse.json({
      success: true,
      data: {
        overview: {
          total: totalStats,
          recent: recentStats,
        },
        bookings: bookingStats,
        revenue: revenueStats,
        upcoming: upcomingBookings,
        activity: recentActivity,
        period: parseInt(period),
      },
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch dashboard data',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// Get total statistics
async function getTotalStats() {
  const [
    totalUsers,
    totalPackages,
    totalBookings,
    totalRevenue,
    activePackages,
    confirmedBookings,
  ] = await Promise.all([
    User.countDocuments(),
    Package.countDocuments(),
    Booking.countDocuments(),
    Payment.aggregate([
      { $match: { status: 'succeeded' } },
      { $group: { _id: null, total: { $sum: '$amount' } } },
    ]),
    Package.countDocuments({ 'availability.isActive': true }),
    Booking.countDocuments({ status: 'confirmed' }),
  ]);
  
  return {
    users: totalUsers,
    packages: totalPackages,
    bookings: totalBookings,
    revenue: totalRevenue[0]?.total || 0,
    activePackages,
    confirmedBookings,
  };
}

// Get recent statistics (within specified period)
async function getRecentStats(startDate) {
  const [
    newUsers,
    newBookings,
    recentRevenue,
    cancelledBookings,
  ] = await Promise.all([
    User.countDocuments({ createdAt: { $gte: startDate } }),
    Booking.countDocuments({ createdAt: { $gte: startDate } }),
    Payment.aggregate([
      {
        $match: {
          status: 'succeeded',
          createdAt: { $gte: startDate },
        },
      },
      { $group: { _id: null, total: { $sum: '$amount' } } },
    ]),
    Booking.countDocuments({
      status: 'cancelled',
      'cancellation.cancelledAt': { $gte: startDate },
    }),
  ]);
  
  return {
    newUsers,
    newBookings,
    revenue: recentRevenue[0]?.total || 0,
    cancelledBookings,
  };
}

// Get booking statistics
async function getBookingStats(startDate) {
  const statusBreakdown = await Booking.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        revenue: { $sum: '$pricing.totalAmount' },
      },
    },
  ]);
  
  const packageBreakdown = await Booking.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $lookup: {
        from: 'packages',
        localField: 'package',
        foreignField: '_id',
        as: 'packageInfo',
      },
    },
    { $unwind: '$packageInfo' },
    {
      $group: {
        _id: '$packageInfo.name',
        count: { $sum: 1 },
        revenue: { $sum: '$pricing.totalAmount' },
      },
    },
    { $sort: { count: -1 } },
    { $limit: 10 },
  ]);
  
  const dailyBookings = await Booking.aggregate([
    { $match: { createdAt: { $gte: startDate } } },
    {
      $group: {
        _id: {
          $dateToString: {
            format: '%Y-%m-%d',
            date: '$createdAt',
          },
        },
        count: { $sum: 1 },
        revenue: { $sum: '$pricing.totalAmount' },
      },
    },
    { $sort: { _id: 1 } },
  ]);
  
  return {
    statusBreakdown: statusBreakdown.reduce((acc, item) => {
      acc[item._id] = {
        count: item.count,
        revenue: item.revenue,
      };
      return acc;
    }, {}),
    packageBreakdown,
    dailyTrend: dailyBookings,
  };
}

// Get revenue statistics
async function getRevenueStats(startDate) {
  const dailyRevenue = await Payment.aggregate([
    {
      $match: {
        status: 'succeeded',
        createdAt: { $gte: startDate },
      },
    },
    {
      $group: {
        _id: {
          $dateToString: {
            format: '%Y-%m-%d',
            date: '$createdAt',
          },
        },
        revenue: { $sum: '$amount' },
        fees: { $sum: '$fees.totalFees' },
        netRevenue: { $sum: '$netAmount' },
        count: { $sum: 1 },
      },
    },
    { $sort: { _id: 1 } },
  ]);
  
  const paymentMethodBreakdown = await Payment.aggregate([
    {
      $match: {
        status: 'succeeded',
        createdAt: { $gte: startDate },
      },
    },
    {
      $group: {
        _id: '$paymentMethod.type',
        count: { $sum: 1 },
        amount: { $sum: '$amount' },
      },
    },
  ]);
  
  const refundStats = await Payment.aggregate([
    {
      $match: {
        status: { $in: ['refunded', 'partially_refunded'] },
        createdAt: { $gte: startDate },
      },
    },
    {
      $group: {
        _id: null,
        totalRefunds: { $sum: '$refund.amount' },
        count: { $sum: 1 },
      },
    },
  ]);
  
  return {
    dailyTrend: dailyRevenue,
    paymentMethods: paymentMethodBreakdown.reduce((acc, item) => {
      acc[item._id] = {
        count: item.count,
        amount: item.amount,
      };
      return acc;
    }, {}),
    refunds: refundStats[0] || { totalRefunds: 0, count: 0 },
  };
}

// Get upcoming bookings
async function getUpcomingBookings() {
  const today = new Date();
  const nextWeek = new Date();
  nextWeek.setDate(today.getDate() + 7);
  
  const upcomingBookings = await Booking.find({
    'dates.checkIn': {
      $gte: today,
      $lte: nextWeek,
    },
    status: { $in: ['confirmed', 'pending'] },
  })
    .populate('customer', 'name email phone')
    .populate('package', 'name category')
    .sort('dates.checkIn')
    .limit(10)
    .lean();
  
  const checkInsToday = await Booking.countDocuments({
    'dates.checkIn': {
      $gte: new Date(today.setHours(0, 0, 0, 0)),
      $lt: new Date(today.setHours(23, 59, 59, 999)),
    },
    status: 'confirmed',
  });
  
  const checkOutsToday = await Booking.countDocuments({
    'dates.checkOut': {
      $gte: new Date(today.setHours(0, 0, 0, 0)),
      $lt: new Date(today.setHours(23, 59, 59, 999)),
    },
    status: 'checked_in',
  });
  
  return {
    bookings: upcomingBookings,
    checkInsToday,
    checkOutsToday,
  };
}

// Get recent activity
async function getRecentActivity() {
  const recentBookings = await Booking.find()
    .populate('customer', 'name email')
    .populate('package', 'name')
    .sort('-createdAt')
    .limit(5)
    .lean();
  
  const recentPayments = await Payment.find({ status: 'succeeded' })
    .populate('customer', 'name email')
    .populate('booking', 'bookingNumber')
    .sort('-createdAt')
    .limit(5)
    .lean();
  
  const recentUsers = await User.find()
    .select('name email role createdAt')
    .sort('-createdAt')
    .limit(5)
    .lean();
  
  // Combine and sort all activities
  const activities = [
    ...recentBookings.map(booking => ({
      type: 'booking',
      action: 'created',
      data: booking,
      timestamp: booking.createdAt,
    })),
    ...recentPayments.map(payment => ({
      type: 'payment',
      action: 'completed',
      data: payment,
      timestamp: payment.createdAt,
    })),
    ...recentUsers.map(user => ({
      type: 'user',
      action: 'registered',
      data: user,
      timestamp: user.createdAt,
    })),
  ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp)).slice(0, 10);
  
  return activities;
}
