import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { storage } from '@/lib/firebase';
import fs from 'fs';
import path from 'path';

/**
 * Upload file to Firebase Storage with local fallback
 */
export async function uploadFile(file, folder = 'general', filename = null) {
  try {
    // Generate filename if not provided
    if (!filename) {
      const timestamp = Date.now();
      const extension = file.name.split('.').pop();
      filename = `${timestamp}.${extension}`;
    }
    
    const filePath = `elephantisland/${folder}/${filename}`;
    
    // Try Firebase Storage first
    try {
      const storageRef = ref(storage, filePath);
      const snapshot = await uploadBytes(storageRef, file);
      const downloadURL = await getDownloadURL(snapshot.ref);
      
      return {
        success: true,
        url: downloadURL,
        path: filePath,
        storage: 'firebase',
        filename,
      };
    } catch (firebaseError) {
      console.warn('Firebase upload failed, falling back to local storage:', firebaseError);
      
      // Fallback to local storage
      const uploadsDir = path.join(process.cwd(), 'public', 'uploads', folder);
      
      // Ensure directory exists
      if (!fs.existsSync(uploadsDir)) {
        fs.mkdirSync(uploadsDir, { recursive: true });
      }
      
      const localPath = path.join(uploadsDir, filename);
      const buffer = await file.arrayBuffer();
      
      fs.writeFileSync(localPath, Buffer.from(buffer));
      
      const publicUrl = `/uploads/${folder}/${filename}`;
      
      return {
        success: true,
        url: publicUrl,
        path: localPath,
        storage: 'local',
        filename,
      };
    }
  } catch (error) {
    console.error('File upload error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Upload multiple files
 */
export async function uploadMultipleFiles(files, folder = 'general') {
  const results = [];
  
  for (const file of files) {
    const result = await uploadFile(file, folder);
    results.push(result);
  }
  
  return results;
}

/**
 * Delete file from Firebase Storage or local storage
 */
export async function deleteFile(filePath, storage = 'firebase') {
  try {
    if (storage === 'firebase') {
      const storageRef = ref(storage, filePath);
      await deleteObject(storageRef);
    } else {
      // Local storage deletion
      const localPath = path.join(process.cwd(), 'public', filePath);
      if (fs.existsSync(localPath)) {
        fs.unlinkSync(localPath);
      }
    }
    
    return { success: true };
  } catch (error) {
    console.error('File deletion error:', error);
    return {
      success: false,
      error: error.message,
    };
  }
}

/**
 * Get file URL (for local files, ensure they're accessible)
 */
export function getFileUrl(filePath, storage = 'firebase') {
  if (storage === 'firebase') {
    return filePath; // Firebase URLs are already complete
  } else {
    // Local files should start with /uploads/
    return filePath.startsWith('/uploads/') ? filePath : `/uploads/${filePath}`;
  }
}

/**
 * Validate file type and size
 */
export function validateFile(file, options = {}) {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'video/mp4', 'video/webm'],
  } = options;
  
  const errors = [];
  
  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`);
  }
  
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`);
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Process image file (resize, compress, etc.)
 */
export async function processImageFile(file, options = {}) {
  const {
    maxWidth = 1920,
    maxHeight = 1080,
    quality = 0.8,
  } = options;
  
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();
    
    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }
      
      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;
      
      // Draw and compress
      ctx.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          const processedFile = new File([blob], file.name, {
            type: file.type,
            lastModified: Date.now(),
          });
          resolve(processedFile);
        },
        file.type,
        quality
      );
    };
    
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Create file upload API endpoint handler
 */
export function createFileUploadHandler(folder, options = {}) {
  return async (request) => {
    try {
      const formData = await request.formData();
      const files = formData.getAll('files');
      
      if (!files || files.length === 0) {
        return new Response(
          JSON.stringify({
            success: false,
            error: 'No files provided',
          }),
          { status: 400 }
        );
      }
      
      const results = [];
      
      for (const file of files) {
        // Validate file
        const validation = validateFile(file, options);
        if (!validation.isValid) {
          results.push({
            filename: file.name,
            success: false,
            errors: validation.errors,
          });
          continue;
        }
        
        // Upload file
        const uploadResult = await uploadFile(file, folder);
        results.push({
          filename: file.name,
          ...uploadResult,
        });
      }
      
      return new Response(
        JSON.stringify({
          success: true,
          data: results,
        }),
        { status: 200 }
      );
    } catch (error) {
      console.error('File upload handler error:', error);
      return new Response(
        JSON.stringify({
          success: false,
          error: error.message,
        }),
        { status: 500 }
      );
    }
  };
}
