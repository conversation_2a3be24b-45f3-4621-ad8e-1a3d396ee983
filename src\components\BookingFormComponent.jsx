'use client';
import React, { useRef, useState } from 'react';

import { FaUser } from "react-icons/fa"
// import DaysBetweenCalculator from './DaysBetweenCalculator';
import ImageScalerComponent from './ImageScalerComponent';
import { color } from 'framer-motion';
import BookingCalender from './BookingCalender';

const cssText1='text-lg text-xl font-bold uppercase  placeholder:text-white'
const cssText2='text-lg text-xs uppercase placeholder:text-white'

function InputComponent(props) {    
    const refInput=useRef(null)
    const {className1,icon,name,setInput,...others}=props   
     const [selected, setSelected] = useState(null);
     
     const handleChange = (e) => {
        setSelected(e.target.value);
        setInput(prev => ({ ...prev, [name]: e.target.value }));
    };
    // refInput.current?.style{{}}
    // console.log('InputComponent:',refInput.current?.style)
    return(
        <div className="flex items-center h-12 gap-2 mt-1 border-6 border-white rounded-xl">
            {icon && <div className='flex items-center justify-center w-fit h-full bg-white/60 p-1'>{icon}</div>}
            <input 
                ref={refInput} 
                // style={{}}
                checked={selected === others?.name}
                onChange={e=>handleChange(e)} 
                className={`flex items-center w-full mt select-none text-xs justify-center px-2 h-full placeholder:text-white placeholder:text-xs outline-none focus:ring-black ${selected === others?.name && 'text-black'} placeholder:${className1}`} {...others} 
            />
        </div>
    )
}

function InputRadioComponent(props) {    
    const {className1,icon,name,setInput,item,...others}=props    
    // console.log('InputRadioComponent:',className1)
    return(
        <div className="flex flex-col items-center h-fit gap-2 mt-1">
            <label className="flex flex-col  items-center justify-center">
                <ImageScalerComponent src={item.src} alt={'icon'}/>
            </label>
            <div className='flex w-fit h-fit border-6 border-white rounded-full'>
                <input onChange={e=>setInput(prev=>({...prev,category:e.target.value}))} value={item.name} id={item.name} name={name} type='radio' className={`flex  w-5 h-5 border-6 m-1 text-black focus:text-white border-none ${className1} rounded-full`} />
            </div>
        </div>
    )
}

export default function BookingFormComponent() {
    const [input,setInput]=useState({})
    const [error,setError]=useState()
    const [success,setSuccess]=useState()

    const handleSubmit = async (e) => {
        e.preventDefault();
        console.log('handleSubmit:',input)
        try {
          const response = await fetch('/api/bookings', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(input),
          });
          
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          
          const data = await response.json();
          setSuccess(data.message);
        } catch (err) {
          setError(err.message);
        }
      };


    // console.log(input)
  return (
    <div className='flex text-white w-full h-screen items-center justify-center'>
        <div className='flex max-w-5xl h-[680px]'>
            <div className='flex flex-col w-1/2 h-full justify-between px-2'>
                <div className='flex flex-col w-[80%] h-full justify-between px-2'>
                    <span className='text-5xl uppercase leading-14 tracking-tight font-medium'>fill the form and send booking request</span>
                    <div className='flex w-86 flex-col leading-0'>
                        <span className='text-3xl tracking-tight font-light leading-8'>Upon submission and approvalof your request, an email link will be sent to you  to process payment</span>
                    </div>
                    <div className='flex w-60 flex-col leading-0'>
                        <span className='text-xl W-20 leading-3 tracking-tight uppercase font-light'>"Ensuring your stay at <span className='text-2xl uppercase font-bold tracking-tight'>elephant island</span>"</span>
                    </div>
                    <div className='flex w-60 flex-col leading-0'>
                        <span className='uppercase leading-5 text-xs'>should there be any queries please contact us at (+267)76123456 or email</span>
                        <span className='lowercase leading-5 font-bold text-xs'><EMAIL></span>
                    </div>
                </div>
            </div>
            <form className='flex flex-col gap-3 w-1/2 h-fit px-2 justify-center'>
                <div className='flex flex-col w-full'>
                    <span className={cssText1}>choose category</span>
                    <fieldset className='flex items-center w-full justify-between'>
                        {[
                            {name:'individual',src:'/assets/individuals_btn_ov.png'},
                            {name:'families',src:'/assets/families_btn_ov.png'},
                            {name:'couples',src:'/assets/couples_btn_ov.png'}
                        ].map((i,index)=>(
                            <InputRadioComponent key={index} item={i} setInput={setInput} name='category' value={i} type='radio' />
                        ))}
                    </fieldset>
                </div>
                <div className="flex items-center w-full justify-between gap-4">
                    <div className='flex flex-col w-full gap-1'>
                        <span className={cssText2}>first name</span>
                        <InputComponent setInput={setInput} placeholder='first name' name='firstName' className1={'uppercase'} type='text' />
                    </div>
                    <div className='flex flex-col w-full gap-1'>
                        <span className={cssText2}>surname</span>
                        <InputComponent setInput={setInput} placeholder='surname' name='surname' className1={'uppercase'} type='text' />
                    </div>
                </div>
                <div className='flex flex-col w-full gap-1'>
                    <span className={cssText2}>email</span>
                    <InputComponent setInput={setInput} placeholder='<EMAIL>' name='email' className1={'lowercase'} type='email' />
                </div>
                <div className='flex flex-col w-full gap-1'>
                    <span className={cssText2}>phone</span>
                    <InputComponent setInput={setInput} placeholder='76123456' name='phone' type='number' />
                </div>
                <div className="flex items-center w-full select-none justify-between gap-4">
                    {/* <DaysBetweenCalculator cssText2={cssText2} className1={'uppercase'} setInput={setInput}/> */}
                </div>
                <div className='flex flex-col w-1/2 gap-1'>
                    <div className='flex flex-col w-full gap-1'>
                        <span className={cssText2}>number of guests</span>
                        <InputComponent setInput={setInput} icon={<FaUser className="text-xl"/>} name='numberOfGuests' placeholder='5' type='number' />
                    </div>
                </div>
                <button onClick={handleSubmit} className='flex items-center justify-center w-full h-12 gap-2 mt-3 border-6 border-white rounded-xl cursor-pointer'>submit</button>
            </form>
        </div>
    </div>
  )
}
