'use client';

export default function PackageAvailability({ package: pkg, onPackageUpdate, onHasChanges }) {
  return (
    <div className="space-y-6">
      <div className="text-center py-12">
        <div className="text-6xl mb-4">📅</div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Availability Management</h3>
        <p className="text-gray-600 mb-4">
          Manage package availability, seasonal schedules, and blackout dates.
        </p>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm text-blue-800">
          <strong>Coming Soon:</strong> Advanced availability calendar with seasonal pricing, 
          blackout dates, and booking restrictions.
        </div>
      </div>
    </div>
  );
}
