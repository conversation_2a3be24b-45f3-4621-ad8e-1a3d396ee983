import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Gallery } from '@/models/Store';
import { requireManagerAPI } from '@/lib/auth-utils';

// GET /api/stores - Get all store items with search and filtering (manager/admin only)
export const GET = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const availability = searchParams.get('availability');
    const author = searchParams.get('author');
    const limit = parseInt(searchParams.get('limit')) || 50;
    const page = parseInt(searchParams.get('page')) || 1;
    const sort = searchParams.get('sort') || '-createdAt';
    
    // Build query
    const query = {};
    
    // Search by title, author, or size
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { author: { $regex: search, $options: 'i' } },
        { size: { $regex: search, $options: 'i' } },
      ];
    }
    
    // Filter by availability
    if (availability) {
      query.availability = availability;
    }
    
    // Filter by author
    if (author) {
      query.author = { $regex: author, $options: 'i' };
    }
    
    // Execute query with pagination
    const skip = (page - 1) * limit;
    const items = await Gallery.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Get total count for pagination
    const total = await Gallery.countDocuments(query);
    
    return NextResponse.json({
      success: true,
      data: items,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching store items:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch store items',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// POST /api/stores - Create new store item (manager/admin only)
export const POST = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['title', 'author', 'price', 'image'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation Error',
            message: `${field} is required`,
          },
          { status: 400 }
        );
      }
    }
    
    // Ensure image is an array
    if (!Array.isArray(body.image)) {
      body.image = [body.image];
    }
    
    // Create new store item
    const newStoreItem = new Gallery(body);
    await newStoreItem.save();
    
    return NextResponse.json(
      {
        success: true,
        data: newStoreItem,
        message: 'Store item created successfully',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating store item:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create store item',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// PUT /api/stores - Bulk update store items (manager/admin only)
export const PUT = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    const { items, action } = body;
    
    if (!Array.isArray(items)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'items must be an array',
        },
        { status: 400 }
      );
    }
    
    const results = [];
    
    for (const itemData of items) {
      try {
        const { _id, ...updateData } = itemData;
        
        // Handle bulk actions
        if (action) {
          switch (action) {
            case 'mark_available':
              updateData.availability = 'Available';
              break;
            case 'mark_sold':
              updateData.availability = 'Sold';
              break;
            case 'mark_reserved':
              updateData.availability = 'Reserved';
              break;
          }
        }
        
        const updatedStoreItem = await Gallery.findByIdAndUpdate(
          _id,
          updateData,
          { new: true, runValidators: true }
        );
        
        if (updatedStoreItem) {
          results.push({
            id: _id,
            success: true,
            data: updatedStoreItem,
          });
        } else {
          results.push({
            id: _id,
            success: false,
            error: 'Store item not found',
          });
        }
      } catch (error) {
        results.push({
          id: itemData._id,
          success: false,
          error: error.message,
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk update completed',
    });
  } catch (error) {
    console.error('Error bulk updating store items:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update store items',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// DELETE /api/stores - Bulk delete store items (manager/admin only)
export const DELETE = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    const { ids } = body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'ids must be a non-empty array',
        },
        { status: 400 }
      );
    }
    
    const result = await Gallery.deleteMany({
      _id: { $in: ids }
    });
    
    return NextResponse.json({
      success: true,
      data: {
        deletedCount: result.deletedCount,
        requestedCount: ids.length,
      },
      message: `${result.deletedCount} store items deleted successfully`,
    });
  } catch (error) {
    console.error('Error deleting store items:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete store items',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
