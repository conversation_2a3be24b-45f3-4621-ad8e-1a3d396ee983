import { auth } from '@/auth';
import { NextResponse } from 'next/server';

// Rate limiting storage
const rateLimitMap = new Map();

// Clean up old entries every 15 minutes
setInterval(() => {
  const now = Date.now();
  for (const [key, data] of rateLimitMap.entries()) {
    if (now - data.resetTime > 15 * 60 * 1000) {
      rateLimitMap.delete(key);
    }
  }
}, 15 * 60 * 1000);

// Rate limiting function
function rateLimit(ip, limit = 100, windowMs = 15 * 60 * 1000) {
  const now = Date.now();
  const key = `${ip}`;
  
  if (!rateLimitMap.has(key)) {
    rateLimitMap.set(key, {
      count: 1,
      resetTime: now + windowMs,
    });
    return { allowed: true, remaining: limit - 1 };
  }
  
  const data = rateLimitMap.get(key);
  
  if (now > data.resetTime) {
    // Reset the window
    data.count = 1;
    data.resetTime = now + windowMs;
    return { allowed: true, remaining: limit - 1 };
  }
  
  if (data.count >= limit) {
    return { 
      allowed: false, 
      remaining: 0,
      resetTime: data.resetTime 
    };
  }
  
  data.count++;
  return { 
    allowed: true, 
    remaining: limit - data.count 
  };
}

// Protected routes configuration
const protectedRoutes = {
  '/admin': ['admin'],
  '/admin/': ['admin'],
  '/admin/dashboard': ['admin'],
  '/admin/users': ['admin'],
  '/admin/packages': ['admin', 'manager'],
  '/admin/bookings': ['admin', 'manager'],
  '/admin/payments': ['admin', 'manager'],
  '/admin/clients': ['admin', 'manager'],
  '/admin/analytics': ['admin', 'manager'],
  '/dashboard': ['user', 'manager', 'admin'],
  '/profile': ['user', 'manager', 'admin'],
};

// API routes that require authentication
const protectedApiRoutes = {
  '/api/admin': ['admin'],
  '/api/users': ['admin'],
  '/api/packages': {
    GET: ['public'], // Public can view packages
    POST: ['admin', 'manager'],
    PUT: ['admin', 'manager'],
    DELETE: ['admin'],
  },
  '/api/bookings': {
    GET: ['user', 'manager', 'admin'], // Users can view their own bookings
    POST: ['public'], // Public can create bookings (guest flow)
    PUT: ['manager', 'admin'],
    DELETE: ['admin'],
  },
  '/api/payments': ['admin', 'manager'],
  '/api/clients': ['admin', 'manager'],
};

// Public routes that don't require authentication
const publicRoutes = [
  '/',
  '/packages',
  '/packages/',
  '/booking',
  '/booking/',
  '/auth/signin',
  '/auth/signup',
  '/auth/error',
  '/api/auth',
  '/api/packages', // GET only
];

function isPublicRoute(pathname) {
  return publicRoutes.some(route => {
    if (route.endsWith('/')) {
      return pathname.startsWith(route) || pathname === route.slice(0, -1);
    }
    return pathname === route || pathname.startsWith(route + '/');
  });
}

function getRequiredRoles(pathname, method = 'GET') {
  // Check page routes
  for (const [route, roles] of Object.entries(protectedRoutes)) {
    if (pathname === route || pathname.startsWith(route + '/')) {
      return roles;
    }
  }
  
  // Check API routes
  for (const [route, config] of Object.entries(protectedApiRoutes)) {
    if (pathname.startsWith(route)) {
      if (typeof config === 'object' && !Array.isArray(config)) {
        return config[method] || config.GET || ['admin'];
      }
      return config;
    }
  }
  
  return null;
}

function hasRequiredRole(userRole, requiredRoles) {
  if (!requiredRoles || requiredRoles.includes('public')) {
    return true;
  }
  
  const roleLevels = {
    guest: 0,
    user: 1,
    manager: 2,
    admin: 3,
  };
  
  const userLevel = roleLevels[userRole] || 0;
  
  return requiredRoles.some(role => {
    const requiredLevel = roleLevels[role] || 0;
    return userLevel >= requiredLevel;
  });
}

export default auth((request) => {
  const { pathname } = request.nextUrl;
  const method = request.method;
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
  
  // Apply rate limiting
  const isAuthRoute = pathname.startsWith('/api/auth') || pathname.startsWith('/auth');
  const limit = isAuthRoute ? 20 : 100; // Stricter limits for auth routes
  
  const rateLimitResult = rateLimit(ip, limit);
  
  if (!rateLimitResult.allowed) {
    return new NextResponse(
      JSON.stringify({
        error: 'Too many requests',
        message: 'Rate limit exceeded. Please try again later.',
        resetTime: rateLimitResult.resetTime,
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'X-RateLimit-Limit': limit.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': rateLimitResult.resetTime.toString(),
        },
      }
    );
  }
  
  // Add rate limit headers to response
  const response = NextResponse.next();
  response.headers.set('X-RateLimit-Limit', limit.toString());
  response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
  
  // Add security headers
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Skip middleware for public routes
  if (isPublicRoute(pathname)) {
    return response;
  }
  
  // Get required roles for this route
  const requiredRoles = getRequiredRoles(pathname, method);
  
  // If no specific roles required, allow access
  if (!requiredRoles) {
    return response;
  }
  
  // Check if user is authenticated
  const session = request.auth;
  
  if (!session?.user) {
    // For API routes, return 401
    if (pathname.startsWith('/api/')) {
      return new NextResponse(
        JSON.stringify({
          error: 'Unauthorized',
          message: 'Authentication required',
        }),
        {
          status: 401,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    
    // For page routes, redirect to sign in
    const signInUrl = new URL('/auth/signin', request.url);
    signInUrl.searchParams.set('callbackUrl', pathname);
    return NextResponse.redirect(signInUrl);
  }
  
  // Check if user has required role
  const userRole = session.user.role || 'user';
  
  if (!hasRequiredRole(userRole, requiredRoles)) {
    // For API routes, return 403
    if (pathname.startsWith('/api/')) {
      return new NextResponse(
        JSON.stringify({
          error: 'Forbidden',
          message: 'Insufficient permissions',
          required: requiredRoles,
          current: userRole,
        }),
        {
          status: 403,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );
    }
    
    // For page routes, redirect to unauthorized page
    return NextResponse.redirect(new URL('/auth/unauthorized', request.url));
  }
  
  return response;
});

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public/).*)',
  ],
};
