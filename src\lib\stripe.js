import Stripe from 'stripe';
import { loadStripe } from '@stripe/stripe-js';

// Server-side Stripe instance
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

// Client-side Stripe instance
let stripePromise;
const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);
  }
  return stripePromise;
};

/**
 * Create payment intent for booking
 * @param {Object} booking - Booking details
 * @returns {Promise<Object>} Payment intent
 */
export async function createBookingPaymentIntent(booking) {
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(booking.pricing.total * 100), // Convert to cents
      currency: booking.pricing.currency.toLowerCase(),
      metadata: {
        type: 'booking',
        bookingId: booking._id.toString(),
        userId: booking.user.toString(),
        lodgeId: booking.lodge.toString(),
      },
      description: `Lodge booking: ${booking.bookingNumber}`,
    });
    
    return paymentIntent;
  } catch (error) {
    console.error('Error creating booking payment intent:', error);
    throw error;
  }
}

/**
 * Create payment intent for gallery order
 * @param {Object} order - Order details
 * @returns {Promise<Object>} Payment intent
 */
export async function createOrderPaymentIntent(order) {
  try {
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(order.pricing.total * 100), // Convert to cents
      currency: order.pricing.currency.toLowerCase(),
      metadata: {
        type: 'order',
        orderId: order._id.toString(),
        userId: order.user.toString(),
      },
      description: `Gallery order: ${order.orderNumber}`,
    });
    
    return paymentIntent;
  } catch (error) {
    console.error('Error creating order payment intent:', error);
    throw error;
  }
}

/**
 * Confirm payment intent
 * @param {string} paymentIntentId - Payment intent ID
 * @returns {Promise<Object>} Payment intent
 */
export async function confirmPaymentIntent(paymentIntentId) {
  try {
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
    return paymentIntent;
  } catch (error) {
    console.error('Error confirming payment intent:', error);
    throw error;
  }
}

/**
 * Create refund
 * @param {string} chargeId - Charge ID
 * @param {number} amount - Refund amount in cents
 * @param {string} reason - Refund reason
 * @returns {Promise<Object>} Refund object
 */
export async function createRefund(chargeId, amount, reason = 'requested_by_customer') {
  try {
    const refund = await stripe.refunds.create({
      charge: chargeId,
      amount: Math.round(amount * 100), // Convert to cents
      reason,
    });
    
    return refund;
  } catch (error) {
    console.error('Error creating refund:', error);
    throw error;
  }
}

/**
 * Handle webhook events
 * @param {Object} event - Stripe webhook event
 * @returns {Promise<void>}
 */
export async function handleWebhookEvent(event) {
  try {
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;
      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;
      case 'charge.dispute.created':
        await handleChargeDispute(event.data.object);
        break;
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }
  } catch (error) {
    console.error('Error handling webhook event:', error);
    throw error;
  }
}

/**
 * Handle successful payment
 * @param {Object} paymentIntent - Payment intent object
 */
async function handlePaymentSucceeded(paymentIntent) {
  const { metadata } = paymentIntent;
  
  if (metadata.type === 'booking') {
    // Update booking status
    const Booking = require('../models/Booking').default;
    await Booking.findByIdAndUpdate(metadata.bookingId, {
      'payment.status': 'paid',
      'payment.paidAt': new Date(),
      'payment.stripePaymentIntentId': paymentIntent.id,
      'payment.stripeChargeId': paymentIntent.latest_charge,
      status: 'confirmed',
    });
  } else if (metadata.type === 'order') {
    // Update order status
    const Order = require('../models/Order').default;
    await Order.findByIdAndUpdate(metadata.orderId, {
      'payment.status': 'paid',
      'payment.paidAt': new Date(),
      'payment.stripePaymentIntentId': paymentIntent.id,
      'payment.stripeChargeId': paymentIntent.latest_charge,
      status: 'confirmed',
    });
  }
}

/**
 * Handle failed payment
 * @param {Object} paymentIntent - Payment intent object
 */
async function handlePaymentFailed(paymentIntent) {
  const { metadata } = paymentIntent;
  
  if (metadata.type === 'booking') {
    const Booking = require('../models/Booking').default;
    await Booking.findByIdAndUpdate(metadata.bookingId, {
      'payment.status': 'failed',
      status: 'cancelled',
    });
  } else if (metadata.type === 'order') {
    const Order = require('../models/Order').default;
    await Order.findByIdAndUpdate(metadata.orderId, {
      'payment.status': 'failed',
      status: 'cancelled',
    });
  }
}

/**
 * Handle charge dispute
 * @param {Object} dispute - Dispute object
 */
async function handleChargeDispute(dispute) {
  // Log dispute for manual review
  console.log('Charge dispute created:', dispute);
  // Implement dispute handling logic here
}

/**
 * Calculate application fee (for marketplace scenarios)
 * @param {number} amount - Transaction amount
 * @returns {number} Application fee
 */
export function calculateApplicationFee(amount) {
  // Example: 3% application fee
  return Math.round(amount * 0.03);
}

export { stripe, getStripe };
