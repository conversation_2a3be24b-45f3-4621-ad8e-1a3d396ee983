import { requireManager } from '@/lib/auth-utils';
import ClientManagementDashboard from '@/components/clients/ClientManagementDashboard';

export default async function AdminClientsPage() {
  // Require manager role or higher
  const user = await requireManager();

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="px-4 py-6 sm:px-0">
          <div className="border-b border-gray-200 pb-4">
            <h1 className="text-3xl font-bold leading-tight text-gray-900">
              Client Management
            </h1>
            <p className="mt-2 text-sm text-gray-600">
              Manage client profiles, view booking history, and track communications.
            </p>
          </div>
        </div>

        {/* Client Management Dashboard */}
        <ClientManagementDashboard />
      </div>
    </div>
  );
}
