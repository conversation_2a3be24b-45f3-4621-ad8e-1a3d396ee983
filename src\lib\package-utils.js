import { Package } from '@/models/Package';
import { connectDB } from '@/lib/mongodb';

// Define the 3 predefined package types
export const PREDEFINED_PACKAGES = [
  {
    name: 'Individual',
    slug: 'individual',
    category: 'individual',
    shortDescription: 'Perfect for solo travelers seeking adventure and tranquility',
    description: 'Experience the ultimate solo getaway at Elephant Island Lodge. This package is designed for individual travelers who want to explore, relax, and connect with nature at their own pace. Enjoy personalized service, peaceful surroundings, and the freedom to create your own adventure.',
    pricing: 250, // Default price - can be updated by admin
    duration: {
      nights: 2,
      days: 3,
    },
    maxGuests: 1,
    inclusions: [
      { item: 'Accommodation', description: 'Private room with en-suite bathroom' },
      { item: 'Meals', description: 'All meals included (breakfast, lunch, dinner)' },
      { item: 'Activities', description: 'Access to all lodge activities' },
      { item: 'Transportation', description: 'Airport transfers included' }
    ],
    exclusions: [
      { item: 'Personal expenses', description: 'Souvenirs, spa treatments, etc.' },
      { item: 'Alcoholic beverages', description: 'Premium drinks not included' }
    ],
    availability: {
      isActive: true,
      advanceBookingDays: 1,
      maxAdvanceBookingDays: 365,
    },
    bookingRules: {
      cancellationPolicy: 'moderate',
      depositRequired: 25,
    },
    featured: false,
    priority: 1,
  },
  {
    name: 'Couples',
    slug: 'couples',
    category: 'couples',
    shortDescription: 'Romantic getaway for two with intimate experiences',
    description: 'Escape to paradise with your loved one. Our couples package offers romantic accommodations, intimate dining experiences, and activities designed for two. Create unforgettable memories in the beautiful setting of Elephant Island Lodge.',
    pricing: 450, // Default price - can be updated by admin
    duration: {
      nights: 3,
      days: 4,
    },
    maxGuests: 2,
    inclusions: [
      { item: 'Romantic accommodation', description: 'Luxury suite with private balcony' },
      { item: 'Couples dining', description: 'Romantic dinners and breakfast in bed' },
      { item: 'Spa treatments', description: 'Couples massage and wellness activities' },
      { item: 'Activities for two', description: 'Sunset cruises, nature walks' },
      { item: 'Transportation', description: 'Private airport transfers' }
    ],
    exclusions: [
      { item: 'Personal expenses', description: 'Shopping and additional spa treatments' },
      { item: 'Premium experiences', description: 'Helicopter tours, private guides' }
    ],
    availability: {
      isActive: true,
      advanceBookingDays: 1,
      maxAdvanceBookingDays: 365,
    },
    bookingRules: {
      cancellationPolicy: 'moderate',
      depositRequired: 30,
    },
    featured: true,
    priority: 2,
  },
  {
    name: 'Families',
    slug: 'families',
    category: 'families',
    shortDescription: 'Fun-filled adventure for the whole family',
    description: 'Bring the whole family for an unforgettable adventure at Elephant Island Lodge. Our family package includes activities for all ages, spacious accommodations, and experiences that will create lasting memories for everyone.',
    pricing: 650, // Default price - can be updated by admin
    duration: {
      nights: 4,
      days: 5,
    },
    maxGuests: 6,
    inclusions: [
      { item: 'Family accommodation', description: 'Spacious family rooms or connecting suites' },
      { item: 'All meals', description: 'Family-friendly dining with kids menu' },
      { item: 'Family activities', description: 'Game drives, nature walks, cultural experiences' },
      { item: 'Kids club', description: 'Supervised activities for children' },
      { item: 'Transportation', description: 'Family-friendly vehicle transfers' }
    ],
    exclusions: [
      { item: 'Personal expenses', description: 'Souvenirs and additional activities' },
      { item: 'Babysitting services', description: 'Private childcare not included' }
    ],
    availability: {
      isActive: true,
      advanceBookingDays: 1,
      maxAdvanceBookingDays: 365,
    },
    bookingRules: {
      cancellationPolicy: 'flexible',
      depositRequired: 20,
    },
    featured: false,
    priority: 3,
  }
];

/**
 * Ensure the 3 predefined packages exist in the database
 * Creates them if they don't exist, updates category if needed
 */
export async function ensurePredefinedPackages() {
  try {
    await connectDB();
    
    const results = [];
    
    for (const packageData of PREDEFINED_PACKAGES) {
      // Check if package exists by slug
      let existingPackage = await Package.findOne({ slug: packageData.slug });
      
      if (!existingPackage) {
        // Create new package
        const newPackage = new Package(packageData);
        await newPackage.save();
        results.push({ action: 'created', package: newPackage });
      } else {
        // Update category if it's different (for migration purposes)
        if (existingPackage.category !== packageData.category) {
          existingPackage.category = packageData.category;
          await existingPackage.save();
          results.push({ action: 'updated', package: existingPackage });
        } else {
          results.push({ action: 'exists', package: existingPackage });
        }
      }
    }
    
    return results;
  } catch (error) {
    console.error('Error ensuring predefined packages:', error);
    throw error;
  }
}

/**
 * Get all predefined packages from database
 */
export async function getPredefinedPackages() {
  try {
    await connectDB();
    
    const packages = await Package.find({
      slug: { $in: PREDEFINED_PACKAGES.map(p => p.slug) }
    }).sort({ priority: 1 });
    
    return packages;
  } catch (error) {
    console.error('Error fetching predefined packages:', error);
    throw error;
  }
}

/**
 * Get package by category (individual, couples, families)
 */
export async function getPackageByCategory(category) {
  try {
    await connectDB();
    
    const packageData = await Package.findOne({ category });
    return packageData;
  } catch (error) {
    console.error(`Error fetching package for category ${category}:`, error);
    throw error;
  }
}

/**
 * Check if a package is one of the predefined types
 */
export function isPredefinedPackage(packageSlug) {
  return PREDEFINED_PACKAGES.some(p => p.slug === packageSlug);
}

/**
 * Get predefined package categories for form options
 */
export function getPackageCategories() {
  return PREDEFINED_PACKAGES.map(p => ({
    value: p.category,
    label: p.name,
    slug: p.slug
  }));
}
