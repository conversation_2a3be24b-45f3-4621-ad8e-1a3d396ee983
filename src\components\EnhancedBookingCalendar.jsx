'use client';

import React, { useState, useEffect, useMemo, useCallback, memo } from 'react';
import { MdOutlineDateRange, MdChevronLeft, MdChevronRight } from 'react-icons/md';

function EnhancedBookingCalendar({
  onDateRangeChange,
  bookedDates = [],
  isLoading = false,
  minDate = new Date(),
  maxDate = null,
  className = ''
}) {
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [checkInDate, setCheckInDate] = useState(null);
  const [checkOutDate, setCheckOutDate] = useState(null);
  const [isSelectingCheckOut, setIsSelectingCheckOut] = useState(false);

  // Calculate number of nights when dates change
  useEffect(() => {
    if (checkInDate && checkOutDate) {
      const nights = Math.ceil((checkOutDate - checkInDate) / (1000 * 60 * 60 * 24));
      onDateRangeChange({
        checkIn: checkInDate,
        checkOut: checkOutDate,
        nights: nights
      });
    } else {
      onDateRangeChange({
        checkIn: null,
        checkOut: null,
        nights: 0
      });
    }
  }, [checkInDate, checkOutDate, onDateRangeChange]);

  // Generate calendar data
  const calendarData = useMemo(() => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()));
    
    const days = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      days.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return days;
  }, [currentMonth]);

  // Check if a date is booked
  const isDateBooked = (date) => {
    const dateStr = date.toISOString().split('T')[0];
    return bookedDates.some(bookedDate => {
      const bookedStr = new Date(bookedDate).toISOString().split('T')[0];
      return bookedStr === dateStr;
    });
  };

  // Check if a date is in the past
  const isPastDate = (date) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    return date < today;
  };

  // Check if a date is disabled
  const isDateDisabled = (date) => {
    if (isPastDate(date)) return true;
    if (minDate && date < minDate) return true;
    if (maxDate && date > maxDate) return true;
    if (isDateBooked(date)) return true;
    return false;
  };

  // Check if a date is in the selected range
  const isInSelectedRange = (date) => {
    if (!checkInDate || !checkOutDate) return false;
    return date > checkInDate && date < checkOutDate;
  };

  // Handle date click
  const handleDateClick = (date) => {
    if (isDateDisabled(date)) return;

    if (!checkInDate || (checkInDate && checkOutDate)) {
      // Start new selection
      setCheckInDate(date);
      setCheckOutDate(null);
      setIsSelectingCheckOut(true);
    } else if (isSelectingCheckOut) {
      // Select check-out date
      if (date > checkInDate) {
        // Check if any dates in between are booked
        const daysBetween = [];
        const currentDate = new Date(checkInDate);
        currentDate.setDate(currentDate.getDate() + 1);
        
        while (currentDate < date) {
          if (isDateBooked(currentDate)) {
            // Can't select this range, there's a booking in between
            return;
          }
          daysBetween.push(new Date(currentDate));
          currentDate.setDate(currentDate.getDate() + 1);
        }
        
        setCheckOutDate(date);
        setIsSelectingCheckOut(false);
      } else {
        // Selected date is before check-in, start over
        setCheckInDate(date);
        setCheckOutDate(null);
      }
    }
  };

  // Navigate months
  const navigateMonth = (direction) => {
    const newMonth = new Date(currentMonth);
    newMonth.setMonth(newMonth.getMonth() + direction);
    setCurrentMonth(newMonth);
  };

  // Format month/year
  const formatMonthYear = (date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
    });
  };

  // Get date class names
  const getDateClassName = (date) => {
    let classes = 'w-5 h-5 flex items-center justify-center text-sm rounded-lg cursor-pointer transition-colors ';
    
    if (isDateDisabled(date)) {
      classes += 'text-gray-300 cursor-not-allowed bg-gray-50 ';
    } else if (checkInDate && date.toDateString() === checkInDate.toDateString()) {
      classes += 'bg-blue-600 text-white font-semibold ';
    } else if (checkOutDate && date.toDateString() === checkOutDate.toDateString()) {
      classes += 'bg-blue-600 text-white font-semibold ';
    } else if (isInSelectedRange(date)) {
      classes += 'bg-blue-100 text-blue-800 ';
    } else if (date.getMonth() !== currentMonth.getMonth()) {
      classes += 'text-gray-400 hover:text-gray-600 ';
    } else {
      classes += 'text-gray-900 hover:bg-blue-50 ';
    }
    
    return classes;
  };

  // Clear selection
  const clearSelection = () => {
    setCheckInDate(null);
    setCheckOutDate(null);
    setIsSelectingCheckOut(false);
  };

  if (isLoading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg p-2 text-sm ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-7 gap-2">
            {[...Array(35)].map((_, i) => (
              <div key={i} className="h-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-2 text-sm ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-semibold text-gray-900 flex items-center">
          <MdOutlineDateRange className="mr-2" />
          Select Dates
        </h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => navigateMonth(-1)}
            className="p-1 text-gray-400 hover:text-gray-600"
          >
            <MdChevronLeft className="w-5 h-5" />
          </button>
          <span className="text-sm font-medium text-gray-700 min-w-32 text-center">
            {formatMonthYear(currentMonth)}
          </span>
          <button
            onClick={() => navigateMonth(1)}
            className="p-1 text-gray-400 hover:text-gray-600"
          >
            <MdChevronRight className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Selected dates display */}
      {(checkInDate || checkOutDate) && (
        <div className="mb-4 p-3 bg-blue-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="text-sm">
              <span className="font-medium text-gray-700">Selected: </span>
              <span className="text-blue-600">
                {checkInDate ? checkInDate.toLocaleDateString() : 'Check-in'} 
                {checkInDate && checkOutDate && ' → '}
                {checkOutDate ? checkOutDate.toLocaleDateString() : (checkInDate ? ' (Select check-out)' : '')}
              </span>
              {checkInDate && checkOutDate && (
                <span className="ml-2 text-gray-600">
                  ({Math.ceil((checkOutDate - checkInDate) / (1000 * 60 * 60 * 24))} nights)
                </span>
              )}
            </div>
            <button
              onClick={clearSelection}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              Clear
            </button>
          </div>
        </div>
      )}

      {/* Day headers */}
      <div className="grid grid-cols-7 gap-1 mb-2">
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
          <div key={day} className="text-center text-xs font-medium text-gray-500 py-2">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar grid */}
      <div className="grid grid-cols-7 gap-1">
        {calendarData.map((date, index) => (
          <button
            key={index}
            onClick={() => handleDateClick(date)}
            className={getDateClassName(date)}
            disabled={isDateDisabled(date)}
            title={
              isDateBooked(date) 
                ? 'This date is already booked' 
                : isPastDate(date) 
                  ? 'Past date' 
                  : date.toLocaleDateString()
            }
          >
            {date.getDate()}
          </button>
        ))}
      </div>

      {/* Legend */}
      <div className="mt-4 pt-3 border-t border-gray-200">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-blue-600 rounded"></div>
              <span>Selected</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-blue-100 rounded"></div>
              <span>Range</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-gray-300 rounded"></div>
              <span>Unavailable</span>
            </div>
          </div>
          <span>Click to select check-in, then check-out</span>
        </div>
      </div>
    </div>
  );
}
export default memo(EnhancedBookingCalendar)