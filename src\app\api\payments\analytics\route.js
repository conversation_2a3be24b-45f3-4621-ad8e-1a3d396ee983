import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Payment } from '@/models/Payment';
import { Booking } from '@/models/Booking';
import { requireManagerAPI } from '@/lib/auth-utils';

// GET /api/payments/analytics - Get payment analytics (manager/admin only)
export const GET = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30'; // days
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    
    let dateFilter = {};
    
    if (startDate && endDate) {
      dateFilter = {
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate),
        },
      };
    } else {
      const daysAgo = new Date();
      daysAgo.setDate(daysAgo.getDate() - parseInt(period));
      dateFilter = {
        createdAt: { $gte: daysAgo },
      };
    }
    
    // Get payment analytics
    const analytics = await Promise.all([
      getPaymentOverview(dateFilter),
      getPaymentTrends(dateFilter),
      getPaymentMethodBreakdown(dateFilter),
      getRefundAnalytics(dateFilter),
      getFailureAnalytics(dateFilter),
      getRevenueByPackage(dateFilter),
    ]);
    
    const [
      overview,
      trends,
      paymentMethods,
      refunds,
      failures,
      revenueByPackage,
    ] = analytics;
    
    return NextResponse.json({
      success: true,
      data: {
        overview,
        trends,
        paymentMethods,
        refunds,
        failures,
        revenueByPackage,
        period: parseInt(period),
        dateRange: {
          start: startDate || dateFilter.createdAt.$gte,
          end: endDate || new Date(),
        },
      },
    });
  } catch (error) {
    console.error('Error fetching payment analytics:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch payment analytics',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// Get payment overview statistics
async function getPaymentOverview(dateFilter) {
  const overview = await Payment.aggregate([
    { $match: dateFilter },
    {
      $group: {
        _id: null,
        totalPayments: { $sum: 1 },
        totalAmount: { $sum: '$amount' },
        totalFees: { $sum: '$fees.totalFees' },
        totalNetAmount: { $sum: '$netAmount' },
        successfulPayments: {
          $sum: { $cond: [{ $eq: ['$status', 'succeeded'] }, 1, 0] },
        },
        failedPayments: {
          $sum: { $cond: [{ $eq: ['$status', 'failed'] }, 1, 0] },
        },
        refundedPayments: {
          $sum: { $cond: [{ $in: ['$status', ['refunded', 'partially_refunded']] }, 1, 0] },
        },
        averagePaymentAmount: { $avg: '$amount' },
      },
    },
  ]);
  
  const result = overview[0] || {
    totalPayments: 0,
    totalAmount: 0,
    totalFees: 0,
    totalNetAmount: 0,
    successfulPayments: 0,
    failedPayments: 0,
    refundedPayments: 0,
    averagePaymentAmount: 0,
  };
  
  // Calculate success rate
  result.successRate = result.totalPayments > 0 
    ? (result.successfulPayments / result.totalPayments) * 100 
    : 0;
  
  return result;
}

// Get payment trends over time
async function getPaymentTrends(dateFilter) {
  const trends = await Payment.aggregate([
    { $match: dateFilter },
    {
      $group: {
        _id: {
          date: {
            $dateToString: {
              format: '%Y-%m-%d',
              date: '$createdAt',
            },
          },
          status: '$status',
        },
        count: { $sum: 1 },
        amount: { $sum: '$amount' },
        fees: { $sum: '$fees.totalFees' },
        netAmount: { $sum: '$netAmount' },
      },
    },
    {
      $group: {
        _id: '$_id.date',
        data: {
          $push: {
            status: '$_id.status',
            count: '$count',
            amount: '$amount',
            fees: '$fees',
            netAmount: '$netAmount',
          },
        },
        totalCount: { $sum: '$count' },
        totalAmount: { $sum: '$amount' },
        totalFees: { $sum: '$fees' },
        totalNetAmount: { $sum: '$netAmount' },
      },
    },
    { $sort: { _id: 1 } },
  ]);
  
  return trends;
}

// Get payment method breakdown
async function getPaymentMethodBreakdown(dateFilter) {
  const breakdown = await Payment.aggregate([
    { $match: { ...dateFilter, status: 'succeeded' } },
    {
      $group: {
        _id: '$paymentMethod.type',
        count: { $sum: 1 },
        amount: { $sum: '$amount' },
        averageAmount: { $avg: '$amount' },
        fees: { $sum: '$fees.totalFees' },
      },
    },
    { $sort: { count: -1 } },
  ]);
  
  return breakdown;
}

// Get refund analytics
async function getRefundAnalytics(dateFilter) {
  const refunds = await Payment.aggregate([
    { 
      $match: { 
        ...dateFilter, 
        status: { $in: ['refunded', 'partially_refunded'] } 
      } 
    },
    {
      $group: {
        _id: null,
        totalRefunds: { $sum: 1 },
        totalRefundAmount: { $sum: '$refund.amount' },
        averageRefundAmount: { $avg: '$refund.amount' },
        refundsByReason: {
          $push: {
            reason: '$refund.reason',
            amount: '$refund.amount',
          },
        },
      },
    },
  ]);
  
  // Group refunds by reason
  const refundsByReason = await Payment.aggregate([
    { 
      $match: { 
        ...dateFilter, 
        status: { $in: ['refunded', 'partially_refunded'] } 
      } 
    },
    {
      $group: {
        _id: '$refund.reason',
        count: { $sum: 1 },
        amount: { $sum: '$refund.amount' },
      },
    },
    { $sort: { count: -1 } },
  ]);
  
  return {
    overview: refunds[0] || {
      totalRefunds: 0,
      totalRefundAmount: 0,
      averageRefundAmount: 0,
    },
    byReason: refundsByReason,
  };
}

// Get failure analytics
async function getFailureAnalytics(dateFilter) {
  const failures = await Payment.aggregate([
    { $match: { ...dateFilter, status: 'failed' } },
    {
      $group: {
        _id: '$failure.code',
        count: { $sum: 1 },
        messages: { $addToSet: '$failure.message' },
      },
    },
    { $sort: { count: -1 } },
  ]);
  
  const failureOverview = await Payment.aggregate([
    { $match: { ...dateFilter, status: 'failed' } },
    {
      $group: {
        _id: null,
        totalFailures: { $sum: 1 },
        uniqueFailureCodes: { $addToSet: '$failure.code' },
      },
    },
  ]);
  
  return {
    overview: failureOverview[0] || {
      totalFailures: 0,
      uniqueFailureCodes: [],
    },
    byCode: failures,
  };
}

// Get revenue by package
async function getRevenueByPackage(dateFilter) {
  const revenueByPackage = await Payment.aggregate([
    { $match: { ...dateFilter, status: 'succeeded' } },
    {
      $lookup: {
        from: 'bookings',
        localField: 'booking',
        foreignField: '_id',
        as: 'bookingInfo',
      },
    },
    { $unwind: '$bookingInfo' },
    {
      $lookup: {
        from: 'packages',
        localField: 'bookingInfo.package',
        foreignField: '_id',
        as: 'packageInfo',
      },
    },
    { $unwind: '$packageInfo' },
    {
      $group: {
        _id: {
          packageId: '$packageInfo._id',
          packageName: '$packageInfo.name',
          category: '$packageInfo.category',
        },
        totalRevenue: { $sum: '$amount' },
        totalBookings: { $sum: 1 },
        averageBookingValue: { $avg: '$amount' },
        totalFees: { $sum: '$fees.totalFees' },
        netRevenue: { $sum: '$netAmount' },
      },
    },
    { $sort: { totalRevenue: -1 } },
    { $limit: 20 },
  ]);
  
  return revenueByPackage;
}
