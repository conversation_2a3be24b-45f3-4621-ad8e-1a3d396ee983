import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { User } from '@/models/User';
import { requireAuthAPI } from '@/lib/auth-utils';
import { 
  createOrRetrieveCustomer, 
  getCustomerPaymentMethods, 
  createSetupIntent,
  detachPaymentMethod 
} from '@/lib/stripe';

// GET /api/payments/methods - Get user's saved payment methods
export const GET = requireAuthAPI(async (request) => {
  try {
    await connectDB();
    
    const userId = request.user.id;
    
    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'User not found',
        },
        { status: 404 }
      );
    }
    
    // Get or create Stripe customer
    const stripeCustomer = await createOrRetrieveCustomer(user);
    
    // Get payment methods
    const paymentMethods = await getCustomerPaymentMethods(stripeCustomer.id);
    
    // Format payment methods for response
    const formattedMethods = paymentMethods.map(method => ({
      id: method.id,
      type: method.type,
      card: method.card ? {
        brand: method.card.brand,
        last4: method.card.last4,
        exp_month: method.card.exp_month,
        exp_year: method.card.exp_year,
        funding: method.card.funding,
      } : null,
      created: method.created,
    }));
    
    return NextResponse.json({
      success: true,
      data: {
        customerId: stripeCustomer.id,
        paymentMethods: formattedMethods,
      },
    });
  } catch (error) {
    console.error('Error fetching payment methods:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch payment methods',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// POST /api/payments/methods - Create setup intent for saving payment method
export const POST = requireAuthAPI(async (request) => {
  try {
    await connectDB();
    
    const userId = request.user.id;
    
    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'User not found',
        },
        { status: 404 }
      );
    }
    
    // Get or create Stripe customer
    const stripeCustomer = await createOrRetrieveCustomer(user);
    
    // Create setup intent
    const setupIntent = await createSetupIntent(stripeCustomer.id);
    
    return NextResponse.json({
      success: true,
      data: {
        setupIntent: {
          id: setupIntent.id,
          client_secret: setupIntent.client_secret,
          status: setupIntent.status,
        },
        customerId: stripeCustomer.id,
      },
      message: 'Setup intent created successfully',
    });
  } catch (error) {
    console.error('Error creating setup intent:', error);
    
    if (error.type?.startsWith('Stripe')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Stripe Error',
          message: error.message,
          code: error.code,
        },
        { status: 402 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create setup intent',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// DELETE /api/payments/methods - Remove saved payment method
export const DELETE = requireAuthAPI(async (request) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const paymentMethodId = searchParams.get('paymentMethodId');
    
    if (!paymentMethodId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'paymentMethodId is required',
        },
        { status: 400 }
      );
    }
    
    const userId = request.user.id;
    
    // Get user details
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'User not found',
        },
        { status: 404 }
      );
    }
    
    // Get Stripe customer
    const stripeCustomer = await createOrRetrieveCustomer(user);
    
    // Verify the payment method belongs to this customer
    const paymentMethods = await getCustomerPaymentMethods(stripeCustomer.id);
    const paymentMethod = paymentMethods.find(pm => pm.id === paymentMethodId);
    
    if (!paymentMethod) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Payment method not found or does not belong to this user',
        },
        { status: 404 }
      );
    }
    
    // Detach payment method
    await detachPaymentMethod(paymentMethodId);
    
    return NextResponse.json({
      success: true,
      message: 'Payment method removed successfully',
      data: {
        paymentMethodId,
      },
    });
  } catch (error) {
    console.error('Error removing payment method:', error);
    
    if (error.type?.startsWith('Stripe')) {
      return NextResponse.json(
        {
          success: false,
          error: 'Stripe Error',
          message: error.message,
          code: error.code,
        },
        { status: 402 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to remove payment method',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
