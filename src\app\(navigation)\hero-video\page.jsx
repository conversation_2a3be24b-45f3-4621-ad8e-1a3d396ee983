import React from 'react';
import Link from 'next/link';
import HeroVideoClient from './HeroVideoClient';
import { settings } from '@/lib/settings';

// Metadata for the page
export const metadata = {
  title: 'Hero Video',
  description: 'Full screen video',
};

export default async function page() {
  let videoUrl = null;

  try {
    // Fetch video data with error handling
    const response = await fetch(`${settings.url}/api/hero-videos`, {
      next: { revalidate: 60 } // Cache for 60 seconds
    });

    if (response.ok) {
      const {data} = await response.json();
      // console.log(data)
      // Find the active video or use the first one
      const activeVideo = data.find(video => video.isActive) || data[0];
      videoUrl = activeVideo?.url;
    }
  } catch (error) {
    // Silently handle fetch errors - the client component will handle the null videoPath
    console.log(error.message)
  }

  // console.log(videoUrl)
  return (
    <div className='flex w-full h-screen items-center justify-center overflow-hidden'>
      <Link href='/360s?id=entrance_360' className='flex z-50 w-fit h-10 border-2 bg-black/30 text-white items-center border-r-gray-50 rounded-full px-6 absolute top-10 left-0 right-0 capitalize mx-auto'>skip</Link>
      <HeroVideoClient videoPath={videoUrl} />
    </div>
  );
}
