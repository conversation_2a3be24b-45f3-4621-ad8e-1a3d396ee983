import { HeroVideo } from '@/libs/mongoDb/models/HeroVideo';
import { withDbConnection } from '../utils/dbConnect';
import { successResponse, errorResponse, handleApiError } from '../utils/apiResponse';

/**
 * GET handler for hero videos
 * @returns All hero videos or filtered by query params
 */
export const GET = withDbConnection(async (req) => {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    const activeOnly = searchParams.get('activeOnly') === 'true';
    
    // If ID is provided, return a specific hero video
    if (id) {
      const heroVideo = await HeroVideo.findById(id);
      if (!heroVideo) {
        return errorResponse('Hero video not found', 404);
      }
      return successResponse(heroVideo);
    }
    
    // Get active hero videos only if requested
    if (activeOnly) {
      const activeVideos = await HeroVideo.find({ isActive: true }).sort({ createdAt: -1 });
      return successResponse(activeVideos);
    }
    
    // Get all hero videos
    const heroVideos = await HeroVideo.find({}).sort({ createdAt: -1 });
    return successResponse(heroVideos);
  } catch (error) {
    return handleApiError(error);
  }
});

/**
 * POST handler to create a new hero video
 */
export const POST = withDbConnection(async (req) => {
  try {
    const body = await req.json();
    
    // Create new hero video
    const newHeroVideo = new HeroVideo(body);
    await newHeroVideo.save();
    
    return successResponse(newHeroVideo, 'Hero video created successfully', 201);
  } catch (error) {
    return handleApiError(error);
  }
});

/**
 * PUT handler to update a hero video
 */
export const PUT = withDbConnection(async (req) => {
  try {
    const body = await req.json();
    const { id } = body;
    
    if (!id) {
      return errorResponse('Hero video ID is required', 400);
    }
    
    // Find and update the hero video
    const updatedHeroVideo = await HeroVideo.findByIdAndUpdate(
      id,
      { $set: body },
      { new: true, runValidators: true }
    );
    
    if (!updatedHeroVideo) {
      return errorResponse('Hero video not found', 404);
    }
    
    return successResponse(updatedHeroVideo, 'Hero video updated successfully');
  } catch (error) {
    return handleApiError(error);
  }
});

/**
 * PATCH handler to toggle active status
 */
export const PATCH = withDbConnection(async (req) => {
  try {
    const body = await req.json();
    const { id, isActive } = body;
    
    if (!id) {
      return errorResponse('Hero video ID is required', 400);
    }
    
    if (typeof isActive !== 'boolean') {
      return errorResponse('isActive must be a boolean value', 400);
    }
    
    // If setting a video to active, deactivate all other videos first
    if (isActive) {
      await HeroVideo.updateMany({}, { isActive: false });
    }
    
    // Update the specified video
    const updatedHeroVideo = await HeroVideo.findByIdAndUpdate(
      id,
      { isActive },
      { new: true }
    );
    
    if (!updatedHeroVideo) {
      return errorResponse('Hero video not found', 404);
    }
    
    return successResponse(updatedHeroVideo, 'Hero video status updated successfully');
  } catch (error) {
    return handleApiError(error);
  }
});

/**
 * DELETE handler to remove a hero video
 */
export const DELETE = withDbConnection(async (req) => {
  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return errorResponse('Hero video ID is required', 400);
    }
    
    // Find and delete the hero video
    const deletedHeroVideo = await HeroVideo.findByIdAndDelete(id);
    
    if (!deletedHeroVideo) {
      return errorResponse('Hero video not found', 404);
    }
    
    return successResponse(null, 'Hero video deleted successfully');
  } catch (error) {
    return handleApiError(error);
  }
});
