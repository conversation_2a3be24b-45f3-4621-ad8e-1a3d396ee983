import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Booking } from '@/models/Booking';
import { Package } from '@/models/Package';
import { requireManagerAPI, createOrUpdateGuestUser } from '@/lib/auth-utils';
import { auth } from '@/auth';

// GET /api/bookings - Get bookings (authenticated users can see their own, managers see all)
export async function GET(request) {
  try {
    await connectDB();
    
    const session = await auth();
    const { searchParams } = new URL(request.url);
    
    const status = searchParams.get('status');
    const customerId = searchParams.get('customer');
    const packageId = searchParams.get('package');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const limit = parseInt(searchParams.get('limit')) || 50;
    const page = parseInt(searchParams.get('page')) || 1;
    const sort = searchParams.get('sort') || '-createdAt';
    
    // Build query based on user role
    const query = {};
    
    // If user is not manager/admin, only show their bookings
    if (session?.user && !['manager', 'admin'].includes(session.user.role)) {
      query.customer = session.user.id;
    }
    
    // Apply filters
    if (status) {
      query.status = status;
    }
    
    if (customerId && ['manager', 'admin'].includes(session?.user?.role)) {
      query.customer = customerId;
    }
    
    if (packageId) {
      query.package = packageId;
    }
    
    if (startDate || endDate) {
      query['dates.checkIn'] = {};
      if (startDate) {
        query['dates.checkIn'].$gte = new Date(startDate);
      }
      if (endDate) {
        query['dates.checkIn'].$lte = new Date(endDate);
      }
    }
    
    // Execute query with pagination
    const skip = (page - 1) * limit;
    const bookings = await Booking.find(query)
      .populate('customer', 'name email phone')
      .populate('package', 'name slug category pricing')
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Get total count for pagination
    const total = await Booking.countDocuments(query);
    
    return NextResponse.json({
      success: true,
      data: bookings,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching bookings:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch bookings',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// POST /api/bookings - Create new booking (public - supports guest bookings)
export async function POST(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    const session = await auth();
    
    // Validate required fields
    const requiredFields = ['packageId', 'checkIn', 'checkOut', 'guests', 'guestDetails'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation Error',
            message: `${field} is required`,
          },
          { status: 400 }
        );
      }
    }
    
    // Get package details
    const package = await Package.findById(body.packageId);
    if (!package) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not Found',
          message: 'Package not found',
        },
        { status: 404 }
      );
    }
    
    // Check package availability
    const checkIn = new Date(body.checkIn);
    const checkOut = new Date(body.checkOut);
    
    if (!package.isAvailableForDates(checkIn, checkOut)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Unavailable',
          message: 'Package is not available for selected dates',
        },
        { status: 409 }
      );
    }
    
    // Check guest capacity
    const totalGuests = body.guests.adults + (body.guests.children || 0);
    if (totalGuests > package.maxGuests) {
      return NextResponse.json(
        {
          success: false,
          error: 'Capacity Exceeded',
          message: `Package capacity is ${package.maxGuests} guests`,
        },
        { status: 409 }
      );
    }
    
    let customerId;
    
    // Handle customer - either authenticated user or create guest user
    if (session?.user) {
      customerId = session.user.id;
    } else {
      // Create guest user
      if (!body.guestInfo || !body.guestInfo.name || !body.guestInfo.email) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation Error',
            message: 'Guest information (name, email) is required for guest bookings',
          },
          { status: 400 }
        );
      }
      
      const guestUser = await createOrUpdateGuestUser(body.guestInfo);
      customerId = guestUser._id;
    }
    
    // Calculate pricing
    const guestType = body.guests.guestType || 'individuals';
    const basePrice = package.getPriceForGuestType(guestType);
    const duration = Math.ceil((checkOut - checkIn) / (1000 * 60 * 60 * 24));
    
    const pricing = {
      basePrice: basePrice,
      taxes: Math.round(basePrice * 0.1), // 10% tax
      fees: 0,
      discounts: 0,
      totalAmount: basePrice + Math.round(basePrice * 0.1),
      currency: 'USD',
    };
    
    // Create booking
    const bookingData = {
      customer: customerId,
      package: body.packageId,
      dates: {
        checkIn,
        checkOut,
        duration,
      },
      guests: {
        adults: body.guests.adults,
        children: body.guests.children || 0,
        total: totalGuests,
        guestType,
      },
      guestDetails: body.guestDetails,
      pricing,
      specialRequests: body.specialRequests || {},
      source: body.source || 'website',
      metadata: {
        ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
      },
    };
    
    const booking = new Booking(bookingData);
    await booking.save();
    
    // Populate the booking for response
    await booking.populate('customer', 'name email phone');
    await booking.populate('package', 'name slug category');
    
    return NextResponse.json(
      {
        success: true,
        data: booking,
        message: 'Booking created successfully',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating booking:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create booking',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// PUT /api/bookings - Bulk update bookings (manager/admin only)
export const PUT = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    const { bookings } = body;
    
    if (!Array.isArray(bookings)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'bookings must be an array',
        },
        { status: 400 }
      );
    }
    
    const results = [];
    
    for (const bookingData of bookings) {
      try {
        const { _id, ...updateData } = bookingData;
        const updatedBooking = await Booking.findByIdAndUpdate(
          _id,
          updateData,
          { new: true, runValidators: true }
        ).populate('customer', 'name email phone')
         .populate('package', 'name slug category');
        
        if (updatedBooking) {
          results.push({
            id: _id,
            success: true,
            data: updatedBooking,
          });
        } else {
          results.push({
            id: _id,
            success: false,
            error: 'Booking not found',
          });
        }
      } catch (error) {
        results.push({
          id: bookingData._id,
          success: false,
          error: error.message,
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk update completed',
    });
  } catch (error) {
    console.error('Error bulk updating bookings:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update bookings',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
