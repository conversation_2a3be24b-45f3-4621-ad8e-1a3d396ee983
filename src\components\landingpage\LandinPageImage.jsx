  'use client'

import Image from "next/image"
import ImageScalerComponent from "../ImageScalerComponent"
import LandingpageComponent from "./LandingpageComponent"

function ImagesDownloadComponent({data}) {
  return(
    <div className='flex relative w-full min-h-32 max-h-36 bg-gray-900 overflow-hidden cursor-pointer rounded-md'>
      <Image priority className='flex brightness-75 scale-100 hover:scale-105 duration-200 ease-linear' src={data?.url} alt='360 image' fill/>
    </div>
  )
}

export default function LandinPageImage() {  
  // console.log('LandinPageImage:',dataUpdate)
  return (
    <>
      <div className="flex h-full w-full overflow-hidden">
        <ImageScalerComponent 
          style={'object-cover md:w-full w-auto h-full'} 
          src={'/assets/hero_image_001.jpg'} 
          alt='landingpage backround image'/>
      </div>
      <LandingpageComponent/>
    </>
  )
}
