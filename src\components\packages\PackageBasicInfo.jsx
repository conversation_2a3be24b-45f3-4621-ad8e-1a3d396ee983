'use client';

import { useState, useEffect } from 'react';
import { isPredefinedPackage } from '@/lib/package-utils';

export default function PackageBasicInfo({ package: pkg, onPackageUpdate, onHasChanges }) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formData, setFormData] = useState({
    name: pkg.name || '',
    shortDescription: pkg.shortDescription || '',
    description: pkg.description || '',
    category: pkg.category || 'accommodation',
    duration: {
      nights: pkg.duration?.nights || 1,
      days: pkg.duration?.days || 2,
    },
    maxGuests: pkg.maxGuests || 2,
    featured: pkg.featured || false,
    priority: pkg.priority || 0,
    tags: pkg.tags?.join(', ') || '',
    seo: {
      metaTitle: pkg.seo?.metaTitle || '',
      metaDescription: pkg.seo?.metaDescription || '',
      keywords: pkg.seo?.keywords?.join(', ') || '',
    },
  });

  const [originalData, setOriginalData] = useState(formData);

  useEffect(() => {
    const hasChanges = JSON.stringify(formData) !== JSON.stringify(originalData);
    onHasChanges(hasChanges);
  }, [formData, originalData, onHasChanges]);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    
    if (name.includes('.')) {
      const [section, field] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [section]: {
          ...prev[section],
          [field]: type === 'checkbox' ? checked : (type === 'number' ? Number(value) : value),
        },
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : (type === 'number' ? Number(value) : value),
      }));
    }
  };

  const handleSave = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Prepare data for API
      const updateData = {
        ...formData,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [],
        seo: {
          ...formData.seo,
          keywords: formData.seo.keywords ? formData.seo.keywords.split(',').map(kw => kw.trim()).filter(Boolean) : [],
        },
      };

      const response = await fetch(`/api/packages/${pkg._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const data = await response.json();

      if (data.success) {
        onPackageUpdate(data.data);
        setOriginalData(formData);
      } else {
        setError(data.message || 'Failed to update package');
      }
    } catch (err) {
      setError('Failed to update package');
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setFormData(originalData);
    setError(null);
  };

  return (
    <div className="space-y-6">
      {/* Basic Information */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700">
              Package Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700">
              Category *
            </label>
            {isPredefinedPackage(pkg.slug) ? (
              <div className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm bg-gray-50 text-gray-600">
                {formData.category === 'individual' && '👤 Individual'}
                {formData.category === 'couples' && '💑 Couples'}
                {formData.category === 'families' && '👨‍👩‍👧‍👦 Families'}
                <span className="ml-2 text-xs text-gray-500">(Read-only for predefined packages)</span>
              </div>
            ) : (
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="individual">👤 Individual</option>
                <option value="couples">💑 Couples</option>
                <option value="families">👨‍👩‍👧‍👦 Families</option>
                <option value="accommodation">🏠 Accommodation</option>
                <option value="experience">🎯 Experience</option>
                <option value="combo">📦 Combo</option>
                <option value="seasonal">🌟 Seasonal</option>
              </select>
            )}
          </div>

          <div className="md:col-span-2">
            <label htmlFor="shortDescription" className="block text-sm font-medium text-gray-700">
              Short Description
            </label>
            <input
              type="text"
              id="shortDescription"
              name="shortDescription"
              value={formData.shortDescription}
              onChange={handleInputChange}
              maxLength={200}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Brief description for listings"
            />
            <p className="text-xs text-gray-500 mt-1">{formData.shortDescription.length}/200 characters</p>
          </div>

          <div className="md:col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Full Description *
            </label>
            <textarea
              id="description"
              name="description"
              rows={6}
              value={formData.description}
              onChange={handleInputChange}
              maxLength={2000}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">{formData.description.length}/2000 characters</p>
          </div>
        </div>
      </div>

      {/* Duration & Capacity */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Duration & Capacity</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label htmlFor="duration.nights" className="block text-sm font-medium text-gray-700">
              Nights *
            </label>
            <input
              type="number"
              id="duration.nights"
              name="duration.nights"
              min="1"
              value={formData.duration.nights}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label htmlFor="duration.days" className="block text-sm font-medium text-gray-700">
              Days *
            </label>
            <input
              type="number"
              id="duration.days"
              name="duration.days"
              min="1"
              value={formData.duration.days}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div>
            <label htmlFor="maxGuests" className="block text-sm font-medium text-gray-700">
              Maximum Guests *
            </label>
            <input
              type="number"
              id="maxGuests"
              name="maxGuests"
              min="1"
              max="20"
              value={formData.maxGuests}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Package Features */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Package Features</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="featured"
                name="featured"
                checked={formData.featured}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="featured" className="ml-2 block text-sm text-gray-900">
                Featured package
              </label>
            </div>
            <div className="text-xs text-gray-500">
              Featured packages appear prominently on the website
            </div>
          </div>

          <div>
            <label htmlFor="priority" className="block text-sm font-medium text-gray-700">
              Priority (0-100)
            </label>
            <input
              type="number"
              id="priority"
              name="priority"
              min="0"
              max="100"
              value={formData.priority}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">Higher numbers appear first in listings</p>
          </div>

          <div>
            <label htmlFor="tags" className="block text-sm font-medium text-gray-700">
              Tags
            </label>
            <input
              type="text"
              id="tags"
              name="tags"
              value={formData.tags}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="romantic, adventure, family-friendly (comma separated)"
            />
          </div>
        </div>
      </div>

      {/* SEO Settings */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">SEO Settings</h3>
        <div className="space-y-4">
          <div>
            <label htmlFor="seo.metaTitle" className="block text-sm font-medium text-gray-700">
              Meta Title
            </label>
            <input
              type="text"
              id="seo.metaTitle"
              name="seo.metaTitle"
              value={formData.seo.metaTitle}
              onChange={handleInputChange}
              maxLength={60}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">{formData.seo.metaTitle.length}/60 characters</p>
          </div>

          <div>
            <label htmlFor="seo.metaDescription" className="block text-sm font-medium text-gray-700">
              Meta Description
            </label>
            <textarea
              id="seo.metaDescription"
              name="seo.metaDescription"
              rows={3}
              value={formData.seo.metaDescription}
              onChange={handleInputChange}
              maxLength={160}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">{formData.seo.metaDescription.length}/160 characters</p>
          </div>

          <div>
            <label htmlFor="seo.keywords" className="block text-sm font-medium text-gray-700">
              Keywords
            </label>
            <input
              type="text"
              id="seo.keywords"
              name="seo.keywords"
              value={formData.seo.keywords}
              onChange={handleInputChange}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="lodge, accommodation, vacation (comma separated)"
            />
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-6 border-t">
        <button
          type="button"
          onClick={handleReset}
          className="bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Reset
        </button>
        <button
          type="button"
          onClick={handleSave}
          disabled={isLoading}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? (
            <div className="flex items-center space-x-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Saving...</span>
            </div>
          ) : (
            'Save Changes'
          )}
        </button>
      </div>
    </div>
  );
}
