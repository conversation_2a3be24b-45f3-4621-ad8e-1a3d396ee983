'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import VideoStream from './VideoStream';

export default function HeroVideoClient({ videoPath }) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Handle case when videoPath is undefined or null
  useEffect(() => {
    if (!videoPath) {
      // If no video path is provided, redirect to 360s after a short delay
      const timer = setTimeout(() => {
        router.push('/360s?id=entrance_360');
      }, 1000);

      return () => clearTimeout(timer);
    } else {
      setIsLoading(false);
    }
  }, [videoPath, router]);

  // Handle video error
  const handleVideoError = () => {
    setHasError(true);
    // Redirect to 360s after a short delay
    setTimeout(() => {
      router.push('/360s?id=entrance_360');
    }, 1000);
  };

  return (
    <>

      <div className="fullscreen-video-container w-svw h-auto">
        {/* {isLoading && !hasError && (
          <div className="absolute inset-0 flex items-center justify-center bg-black">
            <LoadingComponent size="h-16 w-16" />
          </div>
        )} */}

        {hasError && (
          <div className="absolute -z-10 inset-0 flex items-center justify-center bg-black">
            <div className="text-white text-center">
              <p>Video could not be loaded.</p>
              <p>Redirecting to 360° view...</p>
            </div>
          </div>
        )}

        {videoPath && (
          <VideoStream
            src={videoPath}
            autoPlay={true}
            loop={false} // Set to false to enable the redirect when video ends
            muted={true} // Set to true for better autoplay chances (will be forced anyway)
            controls={false} // No controls for cleaner UI
            className="fullscreen-video"
            preload="auto"
            playsInline={true}
            redirectTo="/360s?id=entrance_360" // Redirect to the 360s page when video ends
            onError={handleVideoError}
          />
        )}
      </div>
    </>
  );
}
