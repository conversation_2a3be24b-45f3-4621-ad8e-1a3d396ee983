import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Package } from '@/models/Package';
import { requireManagerAPI } from '@/lib/auth-utils';
import { ensurePredefinedPackages, isPredefinedPackage } from '@/lib/package-utils';

// GET /api/packages - Get all packages (public)
export async function GET(request) {
  try {
    await connectDB();

    // Ensure predefined packages exist
    await ensurePredefinedPackages();
    
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const featured = searchParams.get('featured');
    const active = searchParams.get('active');
    const limit = parseInt(searchParams.get('limit')) || 50;
    const page = parseInt(searchParams.get('page')) || 1;
    const sort = searchParams.get('sort') || '-createdAt';
    
    // Build query
    const query = {};
    
    if (category) {
      query.category = category;
    }
    
    if (featured === 'true') {
      query.featured = true;
    }
    
    if (active !== 'false') {
      query['availability.isActive'] = true;
    }
    
    // Execute query with pagination
    const skip = (page - 1) * limit;
    const packages = await Package.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Get total count for pagination
    const total = await Package.countDocuments(query);
    
    return NextResponse.json({
      success: true,
      data: packages,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching packages:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch packages',
        message: error.message,
      },
      { status: 500 }
    );
  }
}

// POST /api/packages - Create new package (manager/admin only)
// NOTE: Package creation is now restricted to predefined types only
export const POST = requireManagerAPI(async (request) => {
  try {
    await connectDB();

    const body = await request.json();
    console.log('Received package data:', JSON.stringify(body, null, 2));

    // Restrict package creation to predefined types only
    return NextResponse.json(
      {
        success: false,
        error: 'Operation Not Allowed',
        message: 'Package creation is restricted. Only the predefined package types (Individual, Couples, Families) can be managed. Please use the package editor to modify existing packages.',
      },
      { status: 403 }
    );
  } catch (error) {
    console.error('Error creating package:', error);
    
    if (error.code === 11000) {
      return NextResponse.json(
        {
          success: false,
          error: 'Duplicate Error',
          message: 'Package with this name already exists',
        },
        { status: 409 }
      );
    }
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create package',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// PUT /api/packages - Bulk update packages (admin only)
export const PUT = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    const { packages } = body;
    
    if (!Array.isArray(packages)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'packages must be an array',
        },
        { status: 400 }
      );
    }
    
    const results = [];
    
    for (const packageData of packages) {
      try {
        const { _id, ...updateData } = packageData;
        const updatedPackage = await Package.findByIdAndUpdate(
          _id,
          updateData,
          { new: true, runValidators: true }
        );
        
        if (updatedPackage) {
          results.push({
            id: _id,
            success: true,
            data: updatedPackage,
          });
        } else {
          results.push({
            id: _id,
            success: false,
            error: 'Package not found',
          });
        }
      } catch (error) {
        results.push({
          id: packageData._id,
          success: false,
          error: error.message,
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk update completed',
    });
  } catch (error) {
    console.error('Error bulk updating packages:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update packages',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
