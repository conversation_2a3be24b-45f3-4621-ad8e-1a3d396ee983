import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { VideoGallery } from '@/models/VideoGallery';
import { requireManagerAPI } from '@/lib/auth-utils';

// GET /api/video-gallery - Get all video gallery items with search and filtering (manager/admin only)
export const GET = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit')) || 50;
    const page = parseInt(searchParams.get('page')) || 1;
    const sort = searchParams.get('sort') || '-createdAt';
    
    // Build query
    const query = {};
    
    // Search by title
    if (search) {
      query.title = { $regex: search, $options: 'i' };
    }
    
    // Execute query with pagination
    const skip = (page - 1) * limit;
    const items = await VideoGallery.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Get total count for pagination
    const total = await VideoGallery.countDocuments(query);
    
    return NextResponse.json({
      success: true,
      data: items,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching video gallery items:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch video gallery items',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// POST /api/video-gallery - Create new video gallery item (manager/admin only)
export const POST = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['title', 'url'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation Error',
            message: `${field} is required`,
          },
          { status: 400 }
        );
      }
    }
    
    // Create new video gallery item
    const newVideoGalleryItem = new VideoGallery(body);
    await newVideoGalleryItem.save();
    
    return NextResponse.json(
      {
        success: true,
        data: newVideoGalleryItem,
        message: 'Video gallery item created successfully',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating video gallery item:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create video gallery item',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// PUT /api/video-gallery - Bulk update video gallery items (manager/admin only)
export const PUT = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    const { items, action } = body;
    
    if (!Array.isArray(items)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'items must be an array',
        },
        { status: 400 }
      );
    }
    
    const results = [];
    
    for (const itemData of items) {
      try {
        const { _id, ...updateData } = itemData;
        
        const updatedVideoGalleryItem = await VideoGallery.findByIdAndUpdate(
          _id,
          updateData,
          { new: true, runValidators: true }
        );
        
        if (updatedVideoGalleryItem) {
          results.push({
            id: _id,
            success: true,
            data: updatedVideoGalleryItem,
          });
        } else {
          results.push({
            id: _id,
            success: false,
            error: 'Video gallery item not found',
          });
        }
      } catch (error) {
        results.push({
          id: itemData._id,
          success: false,
          error: error.message,
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk update completed',
    });
  } catch (error) {
    console.error('Error bulk updating video gallery items:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update video gallery items',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// DELETE /api/video-gallery - Bulk delete video gallery items (manager/admin only)
export const DELETE = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    const { ids } = body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'ids must be a non-empty array',
        },
        { status: 400 }
      );
    }
    
    const result = await VideoGallery.deleteMany({
      _id: { $in: ids }
    });
    
    return NextResponse.json({
      success: true,
      data: {
        deletedCount: result.deletedCount,
        requestedCount: ids.length,
      },
      message: `${result.deletedCount} video gallery items deleted successfully`,
    });
  } catch (error) {
    console.error('Error deleting video gallery items:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete video gallery items',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
