import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import Stripe from 'stripe';
import connectDB from '@/lib/mongodb';
import { Payment } from '@/models/Payment';
import { Booking } from '@/models/Booking';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request) {
  try {
    const body = await request.text();
    const headersList = headers();
    const signature = headersList.get('stripe-signature');

    if (!signature || !webhookSecret) {
      console.error('Missing Stripe signature or webhook secret');
      return NextResponse.json(
        { error: 'Missing signature or webhook secret' },
        { status: 400 }
      );
    }

    let event;
    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err.message);
      return NextResponse.json(
        { error: 'Invalid signature' },
        { status: 400 }
      );
    }

    await connectDB();

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;

      case 'payment_intent.canceled':
        await handlePaymentCanceled(event.data.object);
        break;

      case 'charge.dispute.created':
        await handleDisputeCreated(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}

async function handlePaymentSucceeded(paymentIntent) {
  try {
    console.log('Payment succeeded:', paymentIntent.id);

    // Find the payment record
    const payment = await Payment.findOne({
      'stripe.paymentIntentId': paymentIntent.id,
    });

    if (!payment) {
      console.error('Payment record not found for PaymentIntent:', paymentIntent.id);
      return;
    }

    // Update payment status
    payment.status = 'succeeded';
    payment.processedAt = new Date();
    
    // Update Stripe details
    if (paymentIntent.charges?.data?.[0]) {
      const charge = paymentIntent.charges.data[0];
      payment.stripe.chargeId = charge.id;
      
      // Update payment method details
      if (charge.payment_method_details?.card) {
        payment.paymentMethod.details.last4 = charge.payment_method_details.card.last4;
        payment.paymentMethod.details.brand = charge.payment_method_details.card.brand;
      }
      
      // Update fees
      payment.fees.stripeFee = charge.application_fee_amount || 0;
      payment.fees.totalFees = payment.fees.stripeFee;
      payment.netAmount = payment.amount - payment.fees.totalFees;
    }

    await payment.save();

    // Update booking payment status
    const booking = await Booking.findById(payment.booking);
    if (booking) {
      booking.payment.status = 'paid';
      booking.payment.paidAmount += payment.amount;
      booking.payment.remainingAmount = booking.pricing.totalAmount - booking.payment.paidAmount;
      
      // If fully paid, confirm the booking
      if (booking.payment.remainingAmount <= 0) {
        booking.status = 'confirmed';
      }
      
      await booking.save();
    }

    console.log('Payment processing completed successfully');
  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

async function handlePaymentFailed(paymentIntent) {
  try {
    console.log('Payment failed:', paymentIntent.id);

    const payment = await Payment.findOne({
      'stripe.paymentIntentId': paymentIntent.id,
    });

    if (!payment) {
      console.error('Payment record not found for PaymentIntent:', paymentIntent.id);
      return;
    }

    // Update payment status
    payment.status = 'failed';
    payment.failedAt = new Date();
    
    // Store failure details
    if (paymentIntent.last_payment_error) {
      payment.failure = {
        code: paymentIntent.last_payment_error.code,
        message: paymentIntent.last_payment_error.message,
        declineCode: paymentIntent.last_payment_error.decline_code,
        type: paymentIntent.last_payment_error.type,
      };
    }

    await payment.save();

    // Update booking payment status
    const booking = await Booking.findById(payment.booking);
    if (booking) {
      booking.payment.status = 'failed';
      await booking.save();
    }

    console.log('Payment failure processing completed');
  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

async function handlePaymentCanceled(paymentIntent) {
  try {
    console.log('Payment canceled:', paymentIntent.id);

    const payment = await Payment.findOne({
      'stripe.paymentIntentId': paymentIntent.id,
    });

    if (!payment) {
      console.error('Payment record not found for PaymentIntent:', paymentIntent.id);
      return;
    }

    // Update payment status
    payment.status = 'cancelled';
    await payment.save();

    // Update booking payment status
    const booking = await Booking.findById(payment.booking);
    if (booking) {
      booking.payment.status = 'cancelled';
      await booking.save();
    }

    console.log('Payment cancellation processing completed');
  } catch (error) {
    console.error('Error handling payment canceled:', error);
  }
}

async function handleDisputeCreated(charge) {
  try {
    console.log('Dispute created for charge:', charge.id);

    const payment = await Payment.findOne({
      'stripe.chargeId': charge.id,
    });

    if (!payment) {
      console.error('Payment record not found for charge:', charge.id);
      return;
    }

    // Update payment with dispute information
    payment.status = 'disputed';
    payment.dispute = {
      amount: charge.amount_disputed,
      reason: charge.dispute?.reason,
      status: charge.dispute?.status,
      disputeId: charge.dispute?.id,
      createdAt: new Date(charge.dispute?.created * 1000),
    };

    await payment.save();

    console.log('Dispute processing completed');
  } catch (error) {
    console.error('Error handling dispute created:', error);
  }
}

async function handleInvoicePaymentSucceeded(invoice) {
  try {
    console.log('Invoice payment succeeded:', invoice.id);
    // Handle subscription or invoice payments if needed
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
  }
}

async function handleSubscriptionDeleted(subscription) {
  try {
    console.log('Subscription deleted:', subscription.id);
    // Handle subscription cancellations if needed
  } catch (error) {
    console.error('Error handling subscription deleted:', error);
  }
}
