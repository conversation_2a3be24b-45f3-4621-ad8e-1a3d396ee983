import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Blog } from '@/models/InfoMarker';
import { requireManagerAPI } from '@/lib/auth-utils';

// GET /api/info-markers - Get all info markers with search and filtering (manager/admin only)
export const GET = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const limit = parseInt(searchParams.get('limit')) || 50;
    const page = parseInt(searchParams.get('page')) || 1;
    const sort = searchParams.get('sort') || '-createdAt';
    
    // Build query
    const query = {};
    
    // Search by title or body content
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { body1: { $regex: search, $options: 'i' } },
        { body2: { $regex: search, $options: 'i' } },
      ];
    }
    
    // Execute query with pagination
    const skip = (page - 1) * limit;
    const items = await Blog.find(query)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();
    
    // Get total count for pagination
    const total = await Blog.countDocuments(query);
    
    return NextResponse.json({
      success: true,
      data: items,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching info markers:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch info markers',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// POST /api/info-markers - Create new info marker (manager/admin only)
export const POST = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    
    // Validate required fields
    const requiredFields = ['title', 'body1', 'body2', 'image'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation Error',
            message: `${field} is required`,
          },
          { status: 400 }
        );
      }
    }
    
    // Create new info marker
    const newInfoMarker = new Blog(body);
    await newInfoMarker.save();
    
    return NextResponse.json(
      {
        success: true,
        data: newInfoMarker,
        message: 'Info marker created successfully',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating info marker:', error);
    
    if (error.name === 'ValidationError') {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: error.message,
          details: error.errors,
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create info marker',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// PUT /api/info-markers - Bulk update info markers (manager/admin only)
export const PUT = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    const { items, action } = body;
    
    if (!Array.isArray(items)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'items must be an array',
        },
        { status: 400 }
      );
    }
    
    const results = [];
    
    for (const itemData of items) {
      try {
        const { _id, ...updateData } = itemData;
        
        const updatedInfoMarker = await Blog.findByIdAndUpdate(
          _id,
          updateData,
          { new: true, runValidators: true }
        );
        
        if (updatedInfoMarker) {
          results.push({
            id: _id,
            success: true,
            data: updatedInfoMarker,
          });
        } else {
          results.push({
            id: _id,
            success: false,
            error: 'Info marker not found',
          });
        }
      } catch (error) {
        results.push({
          id: itemData._id,
          success: false,
          error: error.message,
        });
      }
    }
    
    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk update completed',
    });
  } catch (error) {
    console.error('Error bulk updating info markers:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update info markers',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// DELETE /api/info-markers - Bulk delete info markers (manager/admin only)
export const DELETE = requireManagerAPI(async (request) => {
  try {
    await connectDB();
    
    const body = await request.json();
    const { ids } = body;
    
    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'ids must be a non-empty array',
        },
        { status: 400 }
      );
    }
    
    const result = await Blog.deleteMany({
      _id: { $in: ids }
    });
    
    return NextResponse.json({
      success: true,
      data: {
        deletedCount: result.deletedCount,
        requestedCount: ids.length,
      },
      message: `${result.deletedCount} info markers deleted successfully`,
    });
  } catch (error) {
    console.error('Error deleting info markers:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete info markers',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
