import { requireManager } from '@/lib/auth-utils';
import PackageManagementDashboard from '@/components/packages/PackageManagementDashboard';

export default async function AdminPackagesPage() {
  // Require manager role or higher
  const user = await requireManager();

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="px-4 py-6 sm:px-0">
          <div className="border-b border-gray-200 pb-4">
            <h1 className="text-3xl font-bold leading-tight text-gray-900">
              Package Management
            </h1>
            <p className="mt-2 text-sm text-gray-600">
              Create and manage lodge packages, pricing, availability, and media.
            </p>
          </div>
        </div>

        {/* Package Management Dashboard */}
        <PackageManagementDashboard />
      </div>
    </div>
  );
}
