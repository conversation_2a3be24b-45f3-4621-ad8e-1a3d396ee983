import { NextResponse } from 'next/server';
import { requireManagerAPI } from '@/lib/auth-utils';
import { 
  processEmailNotifications, 
  getEmailNotificationStatus,
  schedulePaymentReminderEmail,
  cancelUnpaidBooking 
} from '@/lib/email-scheduler';

// GET /api/bookings/notifications - Get email notification status for bookings
export const GET = requireManagerAPI(async (request) => {
  try {
    const { searchParams } = new URL(request.url);
    const bookingId = searchParams.get('bookingId');
    const action = searchParams.get('action');
    
    if (action === 'process') {
      // Process all pending notifications
      const result = await processEmailNotifications();
      return NextResponse.json({
        success: true,
        data: result,
        message: 'Email notifications processed',
      });
    }
    
    if (bookingId) {
      // Get status for specific booking
      const status = await getEmailNotificationStatus(bookingId);
      return NextResponse.json({
        success: true,
        data: status,
      });
    }
    
    return NextResponse.json(
      {
        success: false,
        error: 'Missing Parameters',
        message: 'Either bookingId or action=process is required',
      },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error getting notification status:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get notification status',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// POST /api/bookings/notifications - Manually trigger email notifications
export const POST = requireManagerAPI(async (request) => {
  try {
    const body = await request.json();
    const { action, bookingId } = body;
    
    if (!action || !bookingId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'action and bookingId are required',
        },
        { status: 400 }
      );
    }
    
    let result;
    
    switch (action) {
      case 'send_payment_reminder':
        result = await schedulePaymentReminderEmail(bookingId, 0); // Send immediately
        break;
        
      case 'cancel_unpaid_booking':
        result = await cancelUnpaidBooking(bookingId);
        break;
        
      case 'process_all':
        result = await processEmailNotifications();
        break;
        
      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid Action',
            message: 'Valid actions: send_payment_reminder, cancel_unpaid_booking, process_all',
          },
          { status: 400 }
        );
    }
    
    return NextResponse.json({
      success: true,
      data: result,
      message: `Action ${action} completed`,
    });
  } catch (error) {
    console.error('Error processing notification action:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to process notification action',
        message: error.message,
      },
      { status: 500 }
    );
  }
});

// PUT /api/bookings/notifications - Update notification settings
export const PUT = requireManagerAPI(async (request) => {
  try {
    const body = await request.json();
    const { bookingId, settings } = body;
    
    if (!bookingId || !settings) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation Error',
          message: 'bookingId and settings are required',
        },
        { status: 400 }
      );
    }
    
    // This could be extended to update notification preferences
    // For now, just return success
    return NextResponse.json({
      success: true,
      message: 'Notification settings updated',
    });
  } catch (error) {
    console.error('Error updating notification settings:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update notification settings',
        message: error.message,
      },
      { status: 500 }
    );
  }
});
