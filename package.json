{"name": "elephantislandtemplate", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p3003 --experimental-https", "build": "next build", "start": "next start -p3003", "lint": "next lint", "verify-auth": "node scripts/verify-auth-config.js", "test-auth": "npm run verify-auth && echo 'Auth configuration verified!'", "test-middleware": "node scripts/test-auth-middleware.js", "test-auth-full": "npm run verify-auth && npm run test-middleware"}, "dependencies": {"@auth/mongodb-adapter": "^3.9.1", "@stripe/react-stripe-js": "^2.8.1", "@stripe/stripe-js": "^4.7.0", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "firebase": "^10.13.0", "firebase-admin": "^12.3.1", "mongodb": "^6.16.0", "mongoose": "^8.15.1", "next": "15.3.2", "next-auth": "^5.0.0-beta.28", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.0.0", "stripe": "^16.12.0", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "dotenv": "^16.4.5", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4"}}