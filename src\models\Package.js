import mongoose from 'mongoose';

const PackageSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Package name is required'],
    trim: true,
    maxlength: [100, 'Package name cannot exceed 100 characters'],
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
  },
  description: {
    type: String,
    required: [true, 'Package description is required'],
    maxlength: [2000, 'Description cannot exceed 2000 characters'],
  },
  shortDescription: {
    type: String,
    maxlength: [200, 'Short description cannot exceed 200 characters'],
  },
  // Package Type
  category: {
    type: String,
    enum: ['accommodation', 'experience', 'combo', 'seasonal'],
    required: true,
  },
  // Pricing Structure - Fixed prices per package
  pricing: {
    couples: {
      price: {
        type: Number,
        required: true,
        min: [0, 'Price cannot be negative'],
      },
      description: {
        type: String,
        default: 'Price for couples (2 people)',
      },
    },
    individuals: {
      price: {
        type: Number,
        required: true,
        min: [0, 'Price cannot be negative'],
      },
      description: {
        type: String,
        default: 'Price per individual',
      },
    },
    singles: {
      price: {
        type: Number,
        required: true,
        min: [0, 'Price cannot be negative'],
      },
      description: {
        type: String,
        default: 'Price for single occupancy',
      },
    },
  },
  // Package Details
  duration: {
    nights: {
      type: Number,
      required: true,
      min: [1, 'Duration must be at least 1 night'],
    },
    days: {
      type: Number,
      required: true,
      min: [1, 'Duration must be at least 1 day'],
    },
  },
  maxGuests: {
    type: Number,
    required: true,
    min: [1, 'Maximum guests must be at least 1'],
    max: [20, 'Maximum guests cannot exceed 20'],
  },
  // Inclusions
  inclusions: [{
    item: {
      type: String,
      required: true,
    },
    description: String,
  }],
  exclusions: [{
    item: {
      type: String,
      required: true,
    },
    description: String,
  }],
  // Media
  images: [{
    url: String,
    alt: String,
    caption: String,
    isPrimary: {
      type: Boolean,
      default: false,
    },
  }],
  // Availability
  availability: {
    isActive: {
      type: Boolean,
      default: true,
    },
    seasonalAvailability: [{
      startDate: Date,
      endDate: Date,
      isAvailable: {
        type: Boolean,
        default: true,
      },
      reason: String, // e.g., "Maintenance", "High Season"
    }],
    blackoutDates: [{
      date: Date,
      reason: String,
    }],
    advanceBookingDays: {
      type: Number,
      default: 1, // Minimum days in advance to book
    },
    maxAdvanceBookingDays: {
      type: Number,
      default: 365, // Maximum days in advance to book
    },
  },
  // Requirements
  requirements: {
    minimumAge: {
      type: Number,
      default: 0,
    },
    fitnessLevel: {
      type: String,
      enum: ['easy', 'moderate', 'challenging', 'expert'],
      default: 'easy',
    },
    specialRequirements: [String],
  },
  // Location & Logistics
  location: {
    name: String,
    coordinates: {
      latitude: Number,
      longitude: Number,
    },
    address: String,
    meetingPoint: String,
    transportation: {
      included: {
        type: Boolean,
        default: false,
      },
      details: String,
    },
  },
  // Booking Rules
  bookingRules: {
    cancellationPolicy: {
      type: String,
      enum: ['flexible', 'moderate', 'strict'],
      default: 'moderate',
    },
    refundPolicy: String,
    modificationPolicy: String,
    depositRequired: {
      type: Number,
      min: [0, 'Deposit cannot be negative'],
      max: [100, 'Deposit cannot exceed 100%'],
      default: 0, // Percentage of total price
    },
  },
  // SEO & Marketing
  seo: {
    metaTitle: String,
    metaDescription: String,
    keywords: [String],
  },
  tags: [String],
  featured: {
    type: Boolean,
    default: false,
  },
  priority: {
    type: Number,
    default: 0, // Higher numbers appear first
  },
  // Statistics
  stats: {
    totalBookings: {
      type: Number,
      default: 0,
    },
    totalRevenue: {
      type: Number,
      default: 0,
    },
    averageRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5,
    },
    reviewCount: {
      type: Number,
      default: 0,
    },
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// Indexes for performance
PackageSchema.index({ slug: 1 });
PackageSchema.index({ category: 1 });
PackageSchema.index({ 'availability.isActive': 1 });
PackageSchema.index({ featured: 1, priority: -1 });
PackageSchema.index({ createdAt: -1 });
PackageSchema.index({ 'pricing.couples.price': 1 });
PackageSchema.index({ 'pricing.individuals.price': 1 });
PackageSchema.index({ 'pricing.singles.price': 1 });

// Virtual for primary image
PackageSchema.virtual('primaryImage').get(function() {
  const primary = this.images?.find(img => img.isPrimary);
  return primary || this.images?.[0] || null;
});

// Virtual for price range
PackageSchema.virtual('priceRange').get(function() {
  const prices = [
    this.pricing.couples.price,
    this.pricing.individuals.price,
    this.pricing.singles.price,
  ];
  const min = Math.min(...prices);
  const max = Math.max(...prices);
  return { min, max };
});

// Pre-save middleware to generate slug
PackageSchema.pre('save', function(next) {
  if (this.isModified('name') && !this.slug) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  }
  next();
});

// Instance method to check availability for date range
PackageSchema.methods.isAvailableForDates = function(startDate, endDate) {
  if (!this.availability.isActive) return false;
  
  // Check blackout dates
  const start = new Date(startDate);
  const end = new Date(endDate);
  
  for (let blackout of this.availability.blackoutDates) {
    const blackoutDate = new Date(blackout.date);
    if (blackoutDate >= start && blackoutDate <= end) {
      return false;
    }
  }
  
  // Check seasonal availability
  for (let season of this.availability.seasonalAvailability) {
    const seasonStart = new Date(season.startDate);
    const seasonEnd = new Date(season.endDate);
    
    if (start >= seasonStart && end <= seasonEnd) {
      return season.isAvailable;
    }
  }
  
  return true;
};

// Instance method to get price for guest type
PackageSchema.methods.getPriceForGuestType = function(guestType) {
  return this.pricing[guestType]?.price || 0;
};

// Static method to find available packages
PackageSchema.statics.findAvailable = function(startDate, endDate, guestCount) {
  return this.find({
    'availability.isActive': true,
    maxGuests: { $gte: guestCount },
  });
};

export const Package = mongoose.models.Package || mongoose.model('Package', PackageSchema);
