'use client';

import { useState } from 'react';
import PackageActions from './PackageActions';

export default function PackageList({
  packages,
  isLoading,
  pagination,
  onPackageSelect,
  onPackageUpdate,
  onPackageDelete,
  onPageChange,
  onRefresh,
}) {
  const [selectedPackages, setSelectedPackages] = useState([]);
  const [showBulkActions, setShowBulkActions] = useState(false);

  const handleSelectAll = (checked) => {
    if (checked) {
      setSelectedPackages(packages.map(pkg => pkg._id));
    } else {
      setSelectedPackages([]);
    }
  };

  const handleSelectPackage = (packageId, checked) => {
    if (checked) {
      setSelectedPackages(prev => [...prev, packageId]);
    } else {
      setSelectedPackages(prev => prev.filter(id => id !== packageId));
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const getCategoryIcon = (category) => {
    const icons = {
      accommodation: '🏠',
      experience: '🎯',
      combo: '📦',
      seasonal: '🌟',
    };
    return icons[category] || '📋';
  };

  const getStatusIndicator = (pkg) => {
    if (!pkg.availability?.isActive) {
      return <div className="w-2 h-2 bg-red-500 rounded-full" title="Inactive" />;
    }
    return <div className="w-2 h-2 bg-green-500 rounded-full" title="Active" />;
  };

  const getCategoryBadge = (category) => {
    const categoryConfig = {
      accommodation: { bg: 'bg-blue-100', text: 'text-blue-800' },
      experience: { bg: 'bg-green-100', text: 'text-green-800' },
      combo: { bg: 'bg-purple-100', text: 'text-purple-800' },
      seasonal: { bg: 'bg-orange-100', text: 'text-orange-800' },
    };

    const config = categoryConfig[category] || categoryConfig.accommodation;
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {getCategoryIcon(category)} {category.charAt(0).toUpperCase() + category.slice(1)}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">
            Packages ({pagination.total})
          </h3>
          
          {selectedPackages.length > 0 && (
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-500">
                {selectedPackages.length} selected
              </span>
              <button
                onClick={() => setShowBulkActions(!showBulkActions)}
                className="bg-blue-600 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-700"
              >
                Bulk Actions
              </button>
            </div>
          )}
        </div>

        {/* Bulk Actions */}
        {showBulkActions && selectedPackages.length > 0 && (
          <div className="mt-4 p-3 bg-gray-50 rounded-md">
            <div className="flex items-center space-x-3">
              <button className="text-sm text-green-600 hover:text-green-800">
                Activate Selected
              </button>
              <button className="text-sm text-orange-600 hover:text-orange-800">
                Deactivate Selected
              </button>
              <button className="text-sm text-blue-600 hover:text-blue-800">
                Feature Selected
              </button>
              <button className="text-sm text-purple-600 hover:text-purple-800">
                Export Selected
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Package Grid */}
      <div className="p-6">
        {packages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            No packages found matching your criteria.
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {packages.map((pkg) => (
              <div key={pkg._id} className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                {/* Package Image */}
                <div className="relative h-48 bg-gray-200">
                  {pkg.primaryImage ? (
                    <img
                      src={pkg.primaryImage.url}
                      alt={pkg.primaryImage.alt || pkg.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                    </div>
                  )}
                  
                  {/* Status and Featured Indicators */}
                  <div className="absolute top-2 left-2 flex items-center space-x-2">
                    {getStatusIndicator(pkg)}
                    {pkg.featured && (
                      <span className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                        ⭐ Featured
                      </span>
                    )}
                  </div>

                  {/* Selection Checkbox */}
                  <div className="absolute top-2 right-2">
                    <input
                      type="checkbox"
                      checked={selectedPackages.includes(pkg._id)}
                      onChange={(e) => handleSelectPackage(pkg._id, e.target.checked)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>
                </div>

                {/* Package Content */}
                <div className="p-4">
                  <div className="flex items-start justify-between mb-2">
                    <button
                      onClick={() => onPackageSelect(pkg)}
                      className="text-lg font-medium text-gray-900 hover:text-blue-600 text-left"
                    >
                      {pkg.name}
                    </button>
                    <PackageActions
                      package={pkg}
                      onPackageUpdate={onPackageUpdate}
                      onPackageDelete={onPackageDelete}
                      onPackageSelect={onPackageSelect}
                    />
                  </div>

                  <div className="mb-3">
                    {getCategoryBadge(pkg.category)}
                  </div>

                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                    {pkg.shortDescription || pkg.description}
                  </p>

                  {/* Package Details */}
                  <div className="space-y-2 text-sm text-gray-500 mb-3">
                    <div className="flex items-center justify-between">
                      <span>Duration:</span>
                      <span>{pkg.duration?.nights} nights, {pkg.duration?.days} days</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Max Guests:</span>
                      <span>{pkg.maxGuests} people</span>
                    </div>
                  </div>

                  {/* Pricing */}
                  <div className="border-t pt-3">
                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div className="text-center">
                        <div className="font-medium text-gray-900">
                          {formatCurrency(pkg.pricing?.couples?.price || 0)}
                        </div>
                        <div className="text-gray-500">Couples</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-gray-900">
                          {formatCurrency(pkg.pricing?.individuals?.price || 0)}
                        </div>
                        <div className="text-gray-500">Individual</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-gray-900">
                          {formatCurrency(pkg.pricing?.singles?.price || 0)}
                        </div>
                        <div className="text-gray-500">Single</div>
                      </div>
                    </div>
                  </div>

                  {/* Stats */}
                  {(pkg.stats?.totalBookings > 0 || pkg.stats?.totalRevenue > 0) && (
                    <div className="border-t pt-3 mt-3">
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>{pkg.stats.totalBookings || 0} bookings</span>
                        <span>{formatCurrency(pkg.stats.totalRevenue || 0)} revenue</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination.pages > 1 && (
        <div className="px-6 py-4 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing page {pagination.page} of {pagination.pages} 
              ({pagination.total} total packages)
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => onPageChange(Math.max(1, pagination.page - 1))}
                disabled={pagination.page === 1}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Previous
              </button>
              
              {/* Page numbers */}
              {[...Array(Math.min(5, pagination.pages))].map((_, i) => {
                const pageNum = Math.max(1, pagination.page - 2) + i;
                if (pageNum > pagination.pages) return null;
                
                return (
                  <button
                    key={pageNum}
                    onClick={() => onPageChange(pageNum)}
                    className={`px-3 py-1 border rounded-md text-sm ${
                      pageNum === pagination.page
                        ? 'bg-blue-600 text-white border-blue-600'
                        : 'border-gray-300 hover:bg-gray-50'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
              
              <button
                onClick={() => onPageChange(Math.min(pagination.pages, pagination.page + 1))}
                disabled={pagination.page === pagination.pages}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
